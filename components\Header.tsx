import * as React from 'react';

// Declare chrome global for TypeScript
declare const chrome: any;

// Helper for internationalization
const t = (key: string): string => {
    if (typeof chrome !== 'undefined' && chrome.i18n && chrome.i18n.getMessage) {
        try {
            return chrome.i18n.getMessage(key) || key;
        } catch (e) {
            return key;
        }
    }
    return key;
};

const Header: React.FC = () => {
    return (
        <header className="text-center">
            <h1 className="text-2xl font-bold text-white">{t('headerTitle')}</h1>
            <p className="text-sm text-gray-400 mt-1">{t('headerSubtitle')}</p>
        </header>
    );
};

export default Header;