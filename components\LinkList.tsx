import * as React from 'react';
import { useRef, useEffect } from 'react';
import type { ExtractedLink } from '../types';
import { LinkIcon } from './Icons';

interface LinkListProps {
    links: ExtractedLink[];
    selectedLinks: Set<number>;
    onSelectionChange: (index: number) => void;
    onSelectAllChange: () => void;
}

// Declare chrome global for TypeScript
declare const chrome: any;

// Helper for internationalization
const t = (key: string): string => {
    if (typeof chrome !== 'undefined' && chrome.i18n && chrome.i18n.getMessage) {
        try {
            return chrome.i18n.getMessage(key) || key;
        } catch (e) {
            return key;
        }
    }
    return key;
};

const LinkList: React.FC<LinkListProps> = ({ links, selectedLinks, onSelectionChange, onSelectAllChange }) => {
    const selectAllCheckboxRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (selectAllCheckboxRef.current) {
            const numSelected = selectedLinks.size;
            const totalLinks = links.length;
            selectAllCheckboxRef.current.checked = numSelected === totalLinks && totalLinks > 0;
            selectAllCheckboxRef.current.indeterminate = numSelected > 0 && numSelected < totalLinks;
        }
    }, [selectedLinks, links.length]);

    return (
        <div className="bg-gray-800 rounded-lg h-64 border border-gray-700 flex flex-col">
            <div className="flex items-center p-2 border-b border-gray-700 bg-gray-800 sticky top-0 z-10">
                <input
                    type="checkbox"
                    ref={selectAllCheckboxRef}
                    onChange={onSelectAllChange}
                    className="h-4 w-4 rounded border-gray-500 bg-gray-900 text-indigo-600 focus:ring-indigo-500 me-4"
                    aria-label={t('selectAllLabel')}
                    title={t('selectAllLabel')}
                />
                <span className="text-sm font-medium text-gray-300">{t('selectAllLabel')}</span>
            </div>
            <ul className="overflow-y-auto flex-grow">
                {links.map((link, index) => (
                    <li key={index} className="flex items-start p-2 rounded-md hover:bg-gray-700/50 transition-colors duration-150">
                        <input
                            type="checkbox"
                            checked={selectedLinks.has(index)}
                            onChange={() => onSelectionChange(index)}
                            className="h-4 w-4 rounded border-gray-500 bg-gray-900 text-indigo-600 focus:ring-indigo-500 me-3 mt-1 flex-shrink-0"
                            aria-label={`Select link ${index + 1}`}
                        />
                        <span className="text-indigo-400 mt-1 me-3"><LinkIcon /></span>
                        <div className="flex-1 overflow-hidden">
                            <p className="text-sm text-gray-200 font-medium truncate" title={link.text || t('noTextForLink')}>{link.text || <span className="text-gray-500 italic">{t('noTextForLink')}</span>}</p>
                            <a href={link.href} target="_blank" rel="noopener noreferrer" className="text-xs text-gray-400 hover:text-indigo-400 truncate block" title={link.href}>
                                {link.href}
                            </a>
                        </div>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default LinkList;