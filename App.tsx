import * as React from 'react';
import { useState, useCallback, useEffect, useMemo } from 'react';
import jsPDF from 'jspdf';
import type { ExtractedLink } from './types';
import Header from './components/Header';
import ActionButton from './components/ActionButton';
import LinkList from './components/LinkList';
import { CopyIcon, TxtIcon, DocIcon, PdfIcon, ZapIcon, InfoIcon, OpenAllIcon } from './components/Icons';

// Helper for internationalization
const t = (key: string, substitutions?: any): string => {
    if (typeof chrome !== 'undefined' && chrome.i18n && chrome.i18n.getMessage) {
        try {
            return chrome.i18n.getMessage(key, substitutions) || key;
        } catch (e) {
            // This can happen if the key is not found in messages.json
            return key;
        }
    }
    // Fallback for development environments where 'chrome' is not defined.
    return key;
};


// A simple check to see if we are running in a Chrome extension context
const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;

// This function will be injected into the active tab to extract links
const extractLinksFromPage = (): ExtractedLink[] => {
    return Array.from(document.querySelectorAll('a'))
        .filter(anchor => anchor.href) // Ensure link has href
        .map(anchor => ({
            href: anchor.href,
            text: anchor.innerText.trim(),
        }));
};

const App: React.FC = () => {
    const [links, setLinks] = useState<ExtractedLink[]>([]);
    const [selectedLinks, setSelectedLinks] = useState<Set<number>>(new Set());
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [statusMessage, setStatusMessage] = useState<string>('');
    const [error, setError] = useState<string | null>(null);
    const [isInitialState, setIsInitialState] = useState<boolean>(true);


    useEffect(() => {
      if (!isExtension) {
        setStatusMessage('Running in dev mode. Using mock data.');
      }
    }, []);

    const showTemporaryMessage = (message: string) => {
        setStatusMessage(message);
        setTimeout(() => setStatusMessage(''), 3000);
    };

    const handleExtractLinks = useCallback(() => {
        setIsLoading(true);
        setError(null);
        setLinks([]);
        setSelectedLinks(new Set()); // Reset selection on new extraction
        setStatusMessage('');
        setIsInitialState(false);

        if (!isExtension) {
            // Fallback to dummy data for development outside of the extension
            setTimeout(() => {
                const dummyLinks: ExtractedLink[] = [
                    { href: 'https://react.dev/', text: 'React Official Website' },
                    { href: 'https://tailwindcss.com/', text: 'Tailwind CSS' },
                    { href: 'https://www.typescriptlang.org/', text: 'TypeScript' },
                    { href: '#', text: 'This is a mock link for development' }
                ];
                setLinks(dummyLinks);
                setIsLoading(false);
            }, 1000);
            return;
        }

        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            const activeTab = tabs[0];
            if (activeTab && activeTab.id) {
                chrome.scripting.executeScript(
                    {
                        target: { tabId: activeTab.id },
                        func: extractLinksFromPage,
                    },
                    (injectionResults) => {
                        if (chrome.runtime.lastError) {
                            const errorMessage = chrome.runtime.lastError.message;
                            // Specific errors for common, known issues
                            if (errorMessage.includes('Cannot access a chrome:// URL') || errorMessage.includes('cannot be scripted')) {
                                setError(t('unsupportedPageError'));
                            } else if (errorMessage.includes('Cannot access contents of the page')) {
                                setError(t('protectedPageError'));
                            } else {
                                // Generic script injection failure
                                setError(t('injectionFailedError'));
                                console.error(`Link Extractor Pro: Injection failed. ${errorMessage}`);
                            }
                            setIsLoading(false);
                            return;
                        }

                        if (injectionResults && injectionResults[0] && injectionResults[0].result) {
                            const foundLinks = injectionResults[0].result;
                            if (foundLinks.length > 0) {
                                setLinks(foundLinks);
                            } else {
                                setLinks([]); // Explicitly clear links
                                setError(t('noLinksFoundError'));
                            }
                        } else {
                            setError(t('extractionFailedError'));
                        }
                        setIsLoading(false);
                    }
                );
            } else {
                setError(t('noActiveTabError'));
                setIsLoading(false);
            }
        });
    }, []);

    const handleSelectionChange = useCallback((index: number) => {
        setSelectedLinks(prevSelected => {
            const newSelected = new Set(prevSelected);
            if (newSelected.has(index)) {
                newSelected.delete(index);
            } else {
                newSelected.add(index);
            }
            return newSelected;
        });
    }, []);

    const handleSelectAllChange = useCallback(() => {
        if (selectedLinks.size === links.length) {
            setSelectedLinks(new Set());
        } else {
            setSelectedLinks(new Set(links.map((_, i) => i)));
        }
    }, [links, selectedLinks.size]);

    const activeLinks = useMemo(() => {
        return links.filter((_, index) => selectedLinks.has(index));
    }, [links, selectedLinks]);


    const handleCopyToClipboard = useCallback(() => {
        if (activeLinks.length === 0) return;
        const linkText = activeLinks.map(link => link.href).join('\n');
        navigator.clipboard.writeText(linkText)
            .then(() => showTemporaryMessage(t('copiedToClipboardMessage')))
            .catch(() => setError(t('copyFailedError')));
    }, [activeLinks]);

    const handleOpenAllLinks = useCallback(() => {
        if (activeLinks.length === 0) return;

        const openTabs = () => {
            activeLinks.forEach(link => {
                // Basic check for valid URL schemes to avoid chrome://, file://, etc.
                if (link.href.startsWith('http:') || link.href.startsWith('https:')) {
                    if (isExtension) {
                        chrome.tabs.create({ url: link.href, active: false });
                    } else {
                        // Log for dev mode
                        console.log(`DEV MODE: Opening link: ${link.href}`);
                        window.open(link.href, '_blank'); // For dev testing
                    }
                }
            });
            showTemporaryMessage(t('openingLinksMessage'));
        };

        if (activeLinks.length > 15) {
            if (window.confirm(t('openAllConfirmation', [activeLinks.length.toString()]))) {
                openTabs();
            }
        } else {
            openTabs();
        }
    }, [activeLinks]);
    
    const createDownload = (content: string | Blob, filename: string, contentType: string) => {
        const blob = content instanceof Blob ? content : new Blob([content], { type: contentType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const handleExportTXT = useCallback(() => {
        if (activeLinks.length === 0) return;
        const content = activeLinks.map(link => link.href).join('\r\n');
        createDownload(content, 'links.txt', 'text/plain;charset=utf-8');
    }, [activeLinks]);

    const handleExportDOC = useCallback(() => {
        if (activeLinks.length === 0) return;
        let content = '<html xmlns:o=\'urn:schemas-microsoft-com:office:office\' xmlns:w=\'urn:schemas-microsoft-com:office:word\' xmlns=\'http://www.w3.org/TR/REC-html40\'><head><meta charset=\'utf-8\'><title>Export Links</title></head><body>';
        content += '<h1>Extracted Links</h1><ul>';
        activeLinks.forEach(link => {
            const linkText = link.text || link.href;
            content += `<li><a href='${link.href}'>${linkText}</a></li>`;
        });
        content += '</ul></body></html>';
        createDownload(content, 'links.doc', 'application/msword');
    }, [activeLinks]);

    const handleExportPDF = useCallback(() => {
        if (activeLinks.length === 0) return;

        try {
            const doc = new jsPDF();
            
            doc.setFontSize(18);
            doc.text('Extracted Links', 14, 22);
            doc.setFontSize(11);
            
            let y = 30;
            activeLinks.forEach((link, index) => {
                if (y > 280) { // Page break
                    doc.addPage();
                    y = 20;
                }
                const linkText = `${index + 1}. ${link.text || t('noTextForLink')} (${link.href})`;
                // Split text that is too long
                const splitText = doc.splitTextToSize(linkText, 180);
                doc.text(splitText, 14, y);
                y += (splitText.length * 5) + 5;
            });
            doc.save('links.pdf');
        } catch (err) {
            setError(t('pdfFailedError'));
            console.error("Failed to generate PDF:", err);
        }
    }, [activeLinks]);

    const areActionsDisabled = activeLinks.length === 0;

    return (
        <div className="w-[400px] min-h-[500px] bg-gray-900 text-gray-100 flex flex-col font-sans p-4">
            <Header />
            <div className="flex-grow flex flex-col items-center justify-start mt-4">
                <button
                    onClick={handleExtractLinks}
                    disabled={isLoading}
                    className="w-full flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 shadow-lg"
                    aria-label={isLoading ? t('extractingButton') : t('extractLinksButton')}
                >
                    <ZapIcon />
                    <span className="ms-2">{isLoading ? t('extractingButton') : t('extractLinksButton')}</span>
                </button>

                <div className="w-full mt-4 flex-grow">
                    {isLoading && (
                         <div className="flex flex-col items-center justify-center h-full text-gray-400">
                             <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-400"></div>
                             <p className="mt-3">{t('scanningMessage')}</p>
                         </div>
                    )}

                    {error && <div className="text-center text-red-400 mt-4 p-3 bg-red-900/50 rounded-lg">{error}</div>}
                    
                    {isInitialState && !isLoading && !error && (
                         <div className="flex flex-col items-center justify-center h-full text-gray-500">
                            <InfoIcon />
                            <p className="mt-3 text-center">{t('initialStateMessage')}</p>
                        </div>
                    )}

                    {links.length > 0 && !isLoading && (
                        <>
                            <div className="text-center mb-3 flex justify-center items-center gap-4">
                                <div>
                                    <span className="text-gray-400 text-sm">{t('totalLinksFoundLabel')}: </span>
                                    <span className="inline-block bg-gray-700 text-green-400 text-sm font-bold px-3 py-1 rounded-full">
                                        {links.length}
                                    </span>
                                </div>
                                {selectedLinks.size > 0 && (
                                    <div>
                                        <span className="text-gray-400 text-sm">{t('totalSelectedLabel')}: </span>
                                        <span className="inline-block bg-gray-700 text-blue-400 text-sm font-bold px-3 py-1 rounded-full">
                                            {selectedLinks.size}
                                        </span>
                                    </div>
                                )}
                            </div>
                            <LinkList
                                links={links}
                                selectedLinks={selectedLinks}
                                onSelectionChange={handleSelectionChange}
                                onSelectAllChange={handleSelectAllChange}
                            />
                        </>
                    )}
                </div>
            </div>

            {links.length > 0 && !isLoading && (
                <div className="mt-4 pt-4 border-t border-gray-700">
                    <div className="grid grid-cols-3 gap-2">
                        <ActionButton icon={<CopyIcon />} text={t('copyAllButton')} onClick={handleCopyToClipboard} disabled={areActionsDisabled} />
                        <ActionButton icon={<OpenAllIcon />} text={t('openAllButton')} onClick={handleOpenAllLinks} disabled={areActionsDisabled} />
                        <ActionButton icon={<TxtIcon />} text={t('exportTxtButton')} onClick={handleExportTXT} disabled={areActionsDisabled} />
                        <ActionButton icon={<DocIcon />} text={t('exportDocButton')} onClick={handleExportDOC} disabled={areActionsDisabled} />
                        <ActionButton icon={<PdfIcon />} text={t('exportPdfButton')} onClick={handleExportPDF} disabled={areActionsDisabled} />
                    </div>
                </div>
            )}
             {statusMessage && (
                <div className="fixed bottom-4 left-1/2 -translate-x-1/2 bg-green-600 text-white py-2 px-4 rounded-lg shadow-xl text-sm transition-opacity duration-300">
                    {statusMessage}
                </div>
            )}
        </div>
    );
};

export default App;
