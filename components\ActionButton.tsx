import * as React from 'react';

interface ActionButtonProps {
    icon: React.ReactNode;
    text: string;
    onClick: () => void;
    disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({ icon, text, onClick, disabled = false }) => {
    return (
        <button
            onClick={onClick}
            disabled={disabled}
            className="w-full flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-200 font-semibold py-2 px-3 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 text-sm disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-700"
        >
            {icon}
            <span className="ms-2">{text}</span>
        </button>
    );
};

export default ActionButton;