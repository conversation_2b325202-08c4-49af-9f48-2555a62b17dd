/*! tailwindcss v3.4.1 | MIT License | https://tailwindcss.com*/
*,:after,:before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
:after,:before{--tw-content:""}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}
body{margin:0;line-height:inherit}
hr{height:0;color:inherit;border-top-width:1px}
abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}
h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}
a{color:inherit;text-decoration:inherit}
b,strong{font-weight:bolder}
code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sub{bottom:-.25em}
sup{top:-.5em}
table{text-indent:0;border-color:inherit;border-collapse:collapse}
button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}
button,select{text-transform:none}
[type=button],[type=reset],[type=submit],button{-webkit-appearance:button;background-color:transparent;background-image:none}
:-moz-focusring{outline:auto}
:-moz-ui-invalid{box-shadow:none}
progress{vertical-align:baseline}
::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}
[type=search]{-webkit-appearance:textfield;outline-offset:-2px}
::-webkit-search-decoration{-webkit-appearance:none}
::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}
summary{display:list-item}
blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,li,ol,p,pre,ul{margin:0;padding:0}
fieldset{margin:0;padding:0}
legend{padding:0}
menu,ol,ul{list-style:none;padding:0}
dialog{padding:0}
textarea{resize:vertical}
input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}
[role=button],button{cursor:pointer}
:disabled{cursor:default}
audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}
img,video{max-width:100%;height:auto}
[hidden]{display:none}
*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / .5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }
.fixed{position:fixed}
.bottom-4{bottom:1rem}
.left-1\/2{left:50%}
.h-4{height:1rem}
.h-5{height:1.25rem}
.h-8{height:2rem}
.h-10{height:2.5rem}
.h-64{height:16rem}
.h-full{height:100%}
.w-4{width:1rem}
.w-5{width:1.25rem}
.w-8{width:2rem}
.w-10{width:2.5rem}
.w-\[400px\]{width:400px}
.w-full{width:100%}
.flex-1{flex:1 1 0%}
.flex-grow{flex-grow:1}
.animate-spin{animation:spin 1s linear infinite}
@keyframes spin{to{transform:rotate(360deg)}}
.-translate-x-1\/2{--tw-translate-x:-50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}
.flex-col{flex-direction:column}
.items-start{align-items:flex-start}
.items-center{align-items:center}
.justify-center{justify-content:center}
.justify-start{justify-content:flex-start}
.gap-3{gap:.75rem}
.overflow-hidden{overflow:hidden}
.overflow-y-auto{overflow-y:auto}
.rounded-full{border-radius:9999px}
.rounded-lg{border-radius:.5rem}
.rounded-md{border-radius:.375rem}
.border{border-width:1px}
.border-b-2{border-bottom-width:2px}
.border-t{border-top-width:1px}
.bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity))}
.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}
.bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity))}
.bg-green-600{--tw-bg-opacity:1;background-color:rgb(22 163 74 / var(--tw-bg-opacity))}
.bg-indigo-600{--tw-bg-opacity:1;background-color:rgb(79 70 229 / var(--tw-bg-opacity))}
.bg-red-900\/50{background-color:rgb(127 29 29 / .5)}
.p-2{padding:.5rem}
.p-3{padding:.75rem}
.p-4{padding:1rem}
.px-3{padding-left:.75rem;padding-right:.75rem}
.px-4{padding-left:1rem;padding-right:1rem}
.py-2{padding-top:.5rem;padding-bottom:.5rem}
.py-3{padding-top:.75rem;padding-bottom:.75rem}
.pt-4{padding-top:1rem}
.text-center{text-align:center}
.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}
.text-2xl{font-size:1.5rem;line-height:2rem}
.text-sm{font-size:.875rem;line-height:1.25rem}
.text-xs{font-size:.75rem;line-height:1rem}
.font-bold{font-weight:700}
.font-medium{font-weight:500}
.font-semibold{font-weight:600}
.italic{font-style:italic}
.text-gray-100{--tw-text-opacity:1;color:rgb(243 244 246 / var(--tw-text-opacity))}
.text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity))}
.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity))}
.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity))}
.text-green-400{--tw-text-opacity:1;color:rgb(74 222 128 / var(--tw-text-opacity))}
.text-indigo-400{--tw-text-opacity:1;color:rgb(129 140 248 / var(--tw-text-opacity))}
.text-red-400{--tw-text-opacity:1;color:rgb(248 113 113 / var(--tw-text-opacity))}
.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity))}
.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}
.shadow-xl{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}
.outline-none{outline:2px solid transparent;outline-offset:2px}
.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}
.duration-150{transition-duration:.15s}
.duration-200{transition-duration:.2s}
.duration-300{transition-duration:.3s}
.block{display:block}
.flex{display:flex}
.grid{display:grid}
.min-h-\[500px\]{min-height:500px}
.border-gray-700{--tw-border-opacity:1;border-color:rgb(55 65 81 / var(--tw-border-opacity))}
.border-indigo-400{--tw-border-opacity:1;border-color:rgb(129 140 248 / var(--tw-border-opacity))}
.fill-none{fill:none}
.stroke-currentColor{stroke:currentColor}
.stroke-2{stroke-width:2}
.mb-2{margin-bottom:.5rem}
.ms-2{margin-inline-start:.5rem}
.me-3{margin-inline-end:.75rem}
.mt-1{margin-top:.25rem}
.mt-3{margin-top:.75rem}
.mt-4{margin-top:1rem}
.hover\:bg-gray-600:hover{--tw-bg-opacity:1;background-color:rgb(75 85 99 / var(--tw-bg-opacity))}
.hover\:bg-gray-700\/50:hover{background-color:rgb(55 65 81 / .5)}
.hover\:bg-indigo-700:hover{--tw-bg-opacity:1;background-color:rgb(67 56 202 / var(--tw-bg-opacity))}
.hover\:text-indigo-400:hover{--tw-text-opacity:1;color:rgb(129 140 248 / var(--tw-text-opacity))}
.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}
.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}
.focus\:ring-indigo-500:focus{--tw-ring-color:rgb(99 102 241 / var(--tw-ring-opacity))}
.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px}
.focus\:ring-offset-gray-900:focus{--tw-ring-offset-color:#111827}
.disabled\:bg-indigo-400:disabled{--tw-bg-opacity:1;background-color:rgb(129 140 248 / var(--tw-bg-opacity))}