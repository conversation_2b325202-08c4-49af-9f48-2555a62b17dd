/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import{__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED as Internals,createElement,cloneElement,isValidElement}from"react";import{unstable_scheduleCallback,unstable_cancelCallback,unstable_shouldYield,unstable_now,unstable_getCurrentPriorityLevel,unstable_ImmediatePriority,unstable_UserBlockingPriority,unstable_NormalPriority,unstable_LowPriority,unstable_IdlePriority}from"scheduler";var REACT_ELEMENT_TYPE=Symbol.for("react.element"),REACT_PORTAL_TYPE=Symbol.for("react.portal"),REACT_FRAGMENT_TYPE=Symbol.for("react.fragment"),
REACT_STRICT_MODE_TYPE=Symbol.for("react.strict_mode"),REACT_PROFILER_TYPE=Symbol.for("react.profiler"),REACT_PROVIDER_TYPE=Symbol.for("react.provider"),REACT_CONTEXT_TYPE=Symbol.for("react.context"),REACT_FORWARD_REF_TYPE=Symbol.for("react.forward_ref"),REACT_SUSPENSE_TYPE=Symbol.for("react.suspense"),REACT_SUSPENSE_LIST_TYPE=Symbol.for("react.suspense_list"),REACT_MEMO_TYPE=Symbol.for("react.memo"),REACT_LAZY_TYPE=Symbol.for("react.lazy"),REACT_OFFSCREEN_TYPE=Symbol.for("react.offscreen"),
MAYBE_ITERATOR_SYMBOL=Symbol.iterator;function getIteratorFn(maybeIterable){if(null===maybeIterable||"object"!=typeof maybeIterable)return null;maybeIterable=MAYBE_ITERATOR_SYMBOL&&maybeIterable[MAYBE_ITERATOR_SYMBOL]||maybeIterable["@@iterator"];return"function"==typeof maybeIterable?maybeIterable:null}var assign=Object.assign;
function getFiberCurrentPropsFromNode(node){return node[internalPropsKey]||null}
function getInstanceFromNode(node){node=node[internalInstanceKey];return!node||5!==node.tag&&6!==node.tag&&13!==node.tag&&3!==node.tag?null:node}
function getNodeFromInstance(inst){if(5===inst.tag||6===inst.tag)return inst.stateNode;throw Error("getNodeFromInstance: Invalid argument.");}
function getClosestInstanceFromNode(targetNode){if(targetNode[internalInstanceKey])return targetNode[internalInstanceKey];for(var parents=[];!targetNode[internalInstanceKey];){parents.push(targetNode);if(!targetNode.parentNode)return null;targetNode=targetNode.parentNode}for(;targetNode&&!targetNode[internalInstanceKey];targetNode=parents.pop());return targetNode}
function getParent(inst){do inst=inst.return;while(inst&&5!==inst.tag);return inst||null}
function getNextHydratable(node){for(;null!=node;node=node.nextSibling){var nodeName=node.nodeName;if(1===node.nodeType&&"STYLE"!==nodeName&&"SCRIPT"!==nodeName)break}return node}function getNextHydratableSibling(instance){return getNextHydratable(instance.nextSibling)}function getFirstHydratableChild(parentInstance){return getNextHydratable(parentInstance.firstChild)}
function getFirstHydratableChildWithinContainer(container){return getNextHydratable(container.firstChild)}function getFirstHydratableChildWithinSuspenseInstance(suspenseInstance){var node=suspenseInstance.firstChild;return getNextHydratable(8===node.nodeType?node.nextSibling:node)}function getNextHydratableInstanceAfterSuspenseInstance(suspenseInstance){return getNextHydratable(suspenseInstance.nextSibling)}
function is(x,y){return x===y&&0!==x||x!=x&&y!=y}var is$1=is;var scheduleCallback=unstable_scheduleCallback,cancelCallback=unstable_cancelCallback,shouldYield=unstable_shouldYield,requestPaint=function(){},now=unstable_now,getCurrentPriorityLevel=unstable_getCurrentPriorityLevel,ImmediatePriority=unstable_ImmediatePriority,UserBlockingPriority=unstable_UserBlockingPriority,NormalPriority=unstable_NormalPriority,LowPriority=unstable_LowPriority,IdlePriority=unstable_IdlePriority,
rendererID=null,isDehydrated=null,didNotHydrateContainerInstance=null,hasNonHydratedClientRenderedResult=null;
function getClosestRoot(fiber){var currentlyRenderingFiber=null;var root=null;a:for(;;){for(;!fiber.return;)return fiber;var parent=fiber.return;b:for(;;){if(null===parent)return fiber;switch(parent.tag){case 5:if(null===currentlyRenderingFiber){var instance=parent.stateNode;var isContainer=instance.tagName;if("BODY"!==isContainer&&"HTML"!==isContainer){if(isContainer="getRootNode"in instance?instance.getRootNode():instance.ownerDocument)instance=isContainer.body,isContainer=
isContainer.defaultView,isDehydrated=!(!instance||!instance._reactRootContainer),instance=getClosestRoot(fiber),root=null,currentlyRenderingFiber=instance}else currentlyRenderingFiber=null;root=parent;break b}break;case 3:case 27:root=parent.stateNode.containerInfo;break b}parent=parent.return}fiber=parent}}
var TEXT_NODE=3,COMMENT_NODE=8,DOCUMENT_NODE=9,DOCUMENT_FRAGMENT_NODE=11;
function getOwnerDocumentFromRootContainer(rootContainerElement){return 9===rootContainerElement.nodeType?rootContainerElement:rootContainerElement.ownerDocument}
function noop(){}function trapClickOnNonInteractiveElement(node){node.onclick=noop}
function setInitialProperties(domElement,tag,rawProps){var isCustomComponentTag="script"!==tag&&"style"!==tag&&(isCustomComponentTag=isCustomElement(tag,rawProps));1===domElement.nodeType&&domElement.tagName.toUpperCase()!==tag.toUpperCase()||isCustomComponentTag||console.error("Expected server HTML to contain a matching <%s> in <%s>.",tag,domElement.tagName.toLowerCase());isCustomComponentTag={is:rawProps.is}}function diffProperties(domElement,tag,lastRawProps,nextRawProps){return isCustomComponentTag}
function updateProperties(domElement,updatePayload,tag,lastRawProps,nextRawProps){diffProperties();return null}function diffHydratedProperties(domElement,tag,rawProps,parentNamespace,rootContainerInstance){isCustomComponentTag={is:rawProps.is};return null}
function diffHydratedText(textNode,text,isConcurrentMode){textNode.nodeValue!==text&&console.error('Text content did not match. Server: "%s" Client: "%s"',textNode.nodeValue,text)}
function warnForDeletedHydratableElement(parentElement,child){0<deletedNodes.length||console.error("Did not expect server HTML to contain a <%s> in <%s>.",child.localName,parentElement.localName)}
function warnForDeletedHydratableText(parentElement,child){0<deletedNodes.length||console.error('Did not expect server HTML to contain the text node "%s" in <%s>.',child.nodeValue,parentElement.localName)}
function warnForInsertedHydratableElement(parentElement,tag,props){0<deletedNodes.length||console.error("Expected server HTML to contain a matching <%s> in <%s>.",tag,parentElement.localName)}
function warnForInsertedHydratableText(parentElement,text){0<deletedNodes.length||console.error('Expected server HTML to contain a matching text node for "%s" in <%s>.',text,parentElement.localName)}
function restoreControlledState(domElement,tag,props){switch(tag){case "input":var name=props.name,type=props.type,value=props.value;break;case "textarea":name=props.value;break;case "select":name=props.value}}var hasOwnProperty=Object.prototype.hasOwnProperty;function isCustomElement(tagName,props){if(-1===tagName.indexOf("-"))return"string"==typeof props.is;switch(tagName){case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":return!1;
default:return!0}}var eventsEnabled=null,selectionInformation=null;function getSelectionInformation(){if(selectionInformation)return selectionInformation;var activeElement=document.activeElement;var start=-1,end=-1,focusNode=null;try{if(activeElement&&activeElement.nodeName&&"input"===activeElement.nodeName.toLowerCase()){var type=activeElement.type;("text"===type||"search"===type||"tel"===type||"url"===type||"password"===type)&&(start=activeElement.selectionStart,end=activeElement.selectionEnd,
focusNode=activeElement)}else if(activeElement&&activeElement.nodeName&&"textarea"===activeElement.nodeName.toLowerCase())start=activeElement.selectionStart,end=activeElement.selectionEnd,focusNode=activeElement;else if(window.getSelection){var selection=window.getSelection();if(selection&&0<selection.rangeCount){var range=selection.getRangeAt(0);if(activeElement===range.startContainer&&activeElement===range.endContainer)start=range.startOffset,end=range.endOffset;else{var length=
activeElement.textContent.length;range=Math.min(range.startOffset,length);length=void 0===range.endOffset?range:Math.min(range.endOffset,length);start=range;end=length}focusNode=activeElement}}catch(e){}return selectionInformation={focusedElem:focusNode,selectionRange:{start,end}}}
function makePrefixMap(style,events){var prefixes={};events.forEach(function(event){prefixes[event]=style});return prefixes}
var vendorPrefixes={animationend:makePrefixMap("Animation",["-webkit-","-moz-","-ms-","-o-",""].map(function(prefix){return prefix+"AnimationEnd"})),animationiteration:makePrefixMap("Animation",["-webkit-","-moz-","-ms-","-o-",""].map(function(prefix){return prefix+"AnimationIteration"})),animationstart:makePrefixMap("Animation",["-webkit-","-moz-","-ms-","-o-",""].map(function(prefix){return prefix+"AnimationStart"})),transitionend:makePrefixMap("Transition",["-webkit-","-moz-",
"-ms-","-o-",""].map(function(prefix){return prefix+"TransitionEnd"}))},prefixedEventNames={animationend:vendorPrefixes.animationend,animationiteration:vendorPrefixes.animationiteration,animationstart:vendorPrefixes.animationstart,transitionend:vendorPrefixes.transitionend},topLevelEventsToReactNames=new Map,eventPriorities=new Map;
eventPriorities.set("beforeinput",{base:9,discrete:!0});eventPriorities.set("cancel",{base:9,discrete:!0});eventPriorities.set("change",{base:9,discrete:!0});eventPriorities.set("compositionend",{base:9,discrete:!0});eventPriorities.set("compositionstart",{base:9,discrete:!0});eventPriorities.set("compositionupdate",{base:9,discrete:!0});eventPriorities.set("keydown",{base:9,discrete:!0});eventPriorities.set("keypress",{base:9,discrete:!0});eventPriorities.set("keyup",{base:9,discrete:!0});
eventPriorities.set("mousedown",{base:9,discrete:!0});eventPriorities.set("mouseup",{base:9,discrete:!0});eventPriorities.set("paste",{base:9,discrete:!0});eventPriorities.set("focusin",{base:9,discrete:!0});eventPriorities.set("focusout",{base:9,discrete:!0});eventPriorities.set("pointercancel",{base:10,discrete:!0});eventPriorities.set("pointerdown",{base:10,discrete:!0});eventPriorities.set("pointerup",{base:10,discrete:!0});eventPriorities.set("gotpointercapture",{base:10,
discrete:!0});eventPriorities.set("lostpointercapture",{base:10,discrete:!0});eventPriorities.set("pointermove",{base:10});eventPriorities.set("pointerout",{base:10});eventPriorities.set("pointerover",{base:10});eventPriorities.set("scroll",{base:11,passive:!0});eventPriorities.set("toggle",{base:9,discrete:!0});eventPriorities.set("touchcancel",{base:10,discrete:!0});eventPriorities.set("touchstart",{base:10,discrete:!0});eventPriorities.set("touchend",{base:10,discrete:!0});
eventPriorities.set("touchmove",{base:10});eventPriorities.set("transitionend",{base:12});eventPriorities.set("wheel",{base:11,passive:!0});
var discreteEventPairs=[["abort",8],["auxclick",9],["cancel",9],["canplay",8],["canplaythrough",8],["click",9],["close",9],["contextmenu",9],["copy",9],["cut",9],["dblclick",9],["drag",8],["dragend",8],["dragenter",8],["dragexit",8],["dragleave",8],["dragover",8],["dragstart",8],["drop",8],["durationchange",8],["emptied",8],["encrypted",8],["ended",8],["error",8],["fullscreenchange",9],["focusin",9],["focusout",9],["gotpointercapture",10],["input",9],["invalid",9],["keydown",
9],["keypress",9],["keyup",9],["load",8],["loadstart",8],["loadeddata",8],["loadedmetadata",8],["lostpointercapture",10],["mousedown",9],["mouseenter",10],["mouseleave",10],["mousemove",10],["mouseout",10],["mouseover",10],["paste",9],["pause",8],["play",8],["playing",8],["pointercancel",10],["pointerdown",10],["pointerenter",10],["pointerleave",10],["pointermove",10],["pointerout",10],["pointerover",10],["pointerup",10],["progress",8],["ratechange",8],["reset",9],["resize",
8],["seeked",8],["seeking",8],["select",9],["stalled",8],["submit",9],["suspend",8],["textInput","input"],["timeupdate",8],["toggle",9],["touchcancel",10],["touchend",10],["touchmove",10],["touchstart",10],["volumechange",8],["scroll",11],["selectionchange",12],["wheel",11]];
function registerSimplePluginEventsAndSetTheirPriorities(eventTypes,priority){for(var i=0;i<eventTypes.length;i+=2){var topEvent=eventTypes[i],reactName=eventTypes[i+1];reactName=registerSimpleEvent(topEvent,reactName);eventPriorities.set(topEvent,{base:priority,passive:!1,discrete:!1})}return!0}
function registerSimpleEvent(topLevelType,reactName){topLevelEventsToReactNames.set(topLevelType,reactName);return reactName}
(function(){for(var i=0;i<discreteEventPairs.length;i++){var topEvent=discreteEventPairs[i][0],priority=discreteEventPairs[i][1];"string"==typeof topEvent?eventPriorities.set(topEvent,{base:priority,discrete:!0}):eventPriorities.set(topEvent,{base:priority,discrete:!0,capture:!0})}})();var DOMEventPluginOrder=null,DOMEventPluginOrder_1=DOMEventPluginOrder;
function getListener(inst,registrationName){inst=inst.stateNode;if(null===inst)return null;var props=getFiberCurrentPropsFromNode(inst);if(null===props)return null;inst=props[registrationName];a:switch(registrationName){case "onClick":case "onClickCapture":case "onDoubleClick":case "onDoubleClickCapture":case "onMouseDown":case "onMouseDownCapture":case "onMouseMove":case "onMouseMoveCapture":case "onMouseUp":case "onMouseUpCapture":case "onMouseEnter":(props=!props.disabled)||(inst=
inst.type,"button"===inst||"input"===inst||"select"===inst||"textarea"===inst);break a;default:props=!1}if(props)return null;if(inst&&"function"!=typeof inst)throw Error("Expected `"+registrationName+"` listener to be a function, instead got a value of `"+typeof inst+"` type.");return inst}var batchedEventUpdates=function(fn,a,b){switch(getCurrentPriorityLevel()){case ImmediatePriority:return fn(a,b);case UserBlockingPriority:var previousPriority=NormalPriority;break;default:previousPriority=
getCurrentPriorityLevel()}try{return fn(a,b)}finally{}};function getEventTarget(nativeEvent){nativeEvent=nativeEvent.target||nativeEvent.srcElement||window;3.TEXT_NODE===nativeEvent.nodeType&&(nativeEvent=nativeEvent.parentNode);return nativeEvent}
function getEventPriority(domEventName){var priority=eventPriorities.get(domEventName);return void 0===priority?NormalPriority:priority.base}var root=null,startText=null,fallbackText=null;
function getData(){if(fallbackText)return fallbackText;var start,startValue,startLength,end,endValue,endLength;var activeElement=document.activeElement;if(activeElement){var name=activeElement.nodeName;if("BODY"!==name){if("INPUT"===name)start=activeElement.type,"text"===start&&console.log("getData");else if("TEXTAREA"===name)console.log("getData");else return}}var nativeSelection=window.getSelection();start=nativeSelection.anchorNode;end=nativeSelection.focusNode;startValue=
nativeSelection.anchorOffset;endValue=nativeSelection.focusOffset;startLength=0;endLength=0;for(;start&&start!==activeElement;){startLength+=start.textContent.length;start=start.nextSibling}for(;end&&end!==activeElement;){endLength+=end.textContent.length;end=end.nextSibling}var temp=document.createRange();temp.setStart(nativeSelection.anchorNode,nativeSelection.anchorOffset);temp.setEnd(nativeSelection.focusNode,nativeSelection.focusOffset);nativeSelection=temp.toString().split("\n").length-
1;startLength+=nativeSelection;endLength+=nativeSelection;return fallbackText={start:startValue+startLength,end:endValue+endLength}}function FallbackCompositionState(root,startText,fallbackText){}
function isStartish(topLevelType,eventSystemFlags){return"keydown"===topLevelType&&0!==(eventSystemFlags&4)}function isMoveish(topLevelType){return"mousedown"===topLevelType||"mousemove"===topLevelType||"mouseup"===topLevelType}function isSelectEvent(topLevelType){return"selectionchange"===topLevelType}var supportedInputTypes={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};
function isTextInputElement(elem){var nodeName=elem&&elem.nodeName&&elem.nodeName.toLowerCase();return"input"===nodeName?!!supportedInputTypes[elem.type]:"textarea"===nodeName?!0:!1}
function isEventSupported(eventNameSuffix){var eventName="on"+eventNameSuffix;isSupported=eventName in document;isSupported||(isSupported=document.createElement("div"),isSupported.setAttribute(eventName,"return;"),isSupported="function"==typeof isSupported[eventName]);return isSupported}var hasEventPageXY,hasEventClientXY,hasEventLayerXY;
function isListeningToAllDependencies(registrationName,mountAt){var isListening=isListeningTo(mountAt,registrationName);for(var i=0;i<registrationName.length;i++)if(!isListening[i])return!1;return!0}
function isListeningTo(mountAt,registrationName){var listeningSet=getListeningSetForElement(mountAt);return listeningSet.has(registrationName)}
function getListeningSetForElement(element){var listeningSets=element[listeningMarker]||(element[listeningMarker]=new Map);return listeningSets.has(element)?listeningSets.get(element):listeningSets.set(element,new Set)}var listeningMarker="_reactListening"+Math.random().toString(36).slice(2);
function listenTo(registrationName,mountAt){var listeningSet=getListeningSetForElement(mountAt);listeningSet.add(registrationName)}
function listenToAllSupportedEvents(rootContainerElement){if(!rootContainerElement[listeningMarker]){rootContainerElement[listeningMarker]=new Map;var listeningSets=rootContainerElement[listeningMarker];var listeningSet=listeningSets.set(rootContainerElement,new Set);var isSafari=!(!window.safari||!window.safari.pushNotification);topLevelEventsToReactNames.forEach(function(reactName,topEvent){if("selectionchange"!==topEvent){var isCapturePhaseListener="on"===
reactName.slice(0,2);var passive=eventPriorities.get(topEvent);passive=void 0!==passive&&!0===passive.passive;var listener=dispatchDiscreteEvent.bind(null,topEvent,2,rootContainerElement);isSafari&&"click"===topEvent?rootContainerElement.addEventListener(topEvent,listener,isCapturePhaseListener,passive):rootContainerElement.addEventListener(topEvent,listener,isCapturePhaseListener,passive)}})}}
function listenToReactError(rootContainerElement,errorUncaught,errorCaught){var errorListener=dispatchEvent.bind(null,errorUncaught,1,null);var errorCaptureListener=dispatchEvent.bind(null,errorCaught,1,null);rootContainerElement.addEventListener("error",errorListener);rootContainerElement.addEventListener("error",errorCaptureListener,!0)}
function listenToReactErrorForHydration(rootContainerElement){var errorListener=dispatchEvent.bind(null,15,1,null);var errorCaptureListener=dispatchEvent.bind(null,16,1,null);rootContainerElement.addEventListener("error",errorListener);rootContainerElement.addEventListener("error",errorCaptureListener,!0)}
function dispatchDiscreteEvent(topLevelType,eventSystemFlags,container,nativeEvent){var eventPriority=getEventPriority(topLevelType);if(eventPriority===NormalPriority)a:if(null!==currentUpdate&&null!==currentUpdate.renderingFiber){b:switch(currentUpdate.renderingFiber.tag){case 2:case 1:var path=currentUpdate.renderingFiber.stateNode;break b}for(path=currentUpdate.renderingFiber.return;null!==path;){b:switch(path.tag){case 2:case 1:var pathNode=path.stateNode;break b}path=path.return}path=
null}else path=null;else path=null;path=getEventTarget(nativeEvent);10<path.nodeType&&console.error("DRAG_ quién sabe");var a=getClosestInstanceFromNode(path);if(null!==a)c:if(b=a,0===eventPriority)eventPriority=UserBlockingPriority;else if(0!==eventPriority)eventPriority=NormalPriority;else eventPriority=NormalPriority;else b=null;path=null;eventPriority=batchedEventUpdates;a?path=a:b&&(path=b);path&&eventPriority(function(){dispatchEventsForPlugins(topLevelType,eventSystemFlags,
nativeEvent,a,b)})};
function dispatchEvent(topLevelType,eventSystemFlags,container,nativeEvent){dispatchDiscreteEvent(topLevelType,eventSystemFlags,container,nativeEvent)}
function dispatchEventsForPlugins(topLevelType,eventSystemFlags,nativeEvent,targetInst,rootInst){var nativeEventTarget=getEventTarget(nativeEvent);var path=constructDispatchQueue(targetInst,rootInst,topLevelType);if(0<path.length){eventSystemFlags={bubbles:!0,cancelable:!0,detail:0,eventPhase:0,pageX:0,pageY:0,preventDefault:function(){this.defaultPrevented=!0},isDefaultPrevented:function(){return this.defaultPrevented},stopPropagation:function(){this.isPropagationStopped=
function(){return!0}},isPropagationStopped:function(){return!1},target:nativeEventTarget,timeStamp:0,type:topLevelType};processDispatchQueue(path,eventSystemFlags)}}
function processDispatchQueue(dispatchQueue,event){for(var i=0;i<dispatchQueue.length&&!event.isPropagationStopped();i++){var inst=dispatchQueue[i],listener=getListener(inst,event.type);listener(event)}}
function constructDispatchQueue(targetInst,rootInst,topLevelType){for(var path=[],_targetInst=targetInst;null!==_targetInst;){var isDescendant=null!==rootInst&&_targetInst===rootInst;if(isDescendant||"selectionchange"===topLevelType&&null===rootInst)break;path.push(_targetInst);_targetInst=_targetInst.return}return path}var randomKey=Math.random().toString(36).slice(2),internalInstanceKey="__reactFiber$"+randomKey,internalPropsKey="__reactProps$"+randomKey;
var Scheduler_now=now;assign(Internals.ReactCurrentDispatcher,{current:null});assign(Internals.ReactCurrentBatchConfig,{transition:null});var ReactCurrentActQueue$1=Internals.ReactCurrentActQueue,ReactCurrentOwner=Internals.ReactCurrentOwner,ReactDebugCurrentFrame=Internals.ReactDebugCurrentFrame;var IsSomeRendererActing={current:!1};function getWorkInProgressRoot(){return workInProgressRoot}
function getWorkInProgressRootRenderLanes(){return workInProgressRootRenderLanes}var NoContext=0,BatchedContext=1,RenderContext=2,CommitContext=4,executionContext=NoContext;function getExecutionContext(){return executionContext}
function setExecutionContext(context){executionContext|=context}function resetExecutionContext(context){executionContext&=(~context)}function isPrimaryRenderer(rendererID){return rendererID===ReactCurrentRenderer.current}
function getRendererPriorityLevel(rendererID){switch(rendererID){case 1:case 2:case 3:case 4:case 5:return ImmediatePriority;case 6:case 7:return UserBlockingPriority;case 8:case 9:case 10:case 11:return NormalPriority;case 12:case 13:return LowPriority;case 14:case 15:return IdlePriority;default:return NormalPriority}}
function laneToRendererPriority(lanes){return 0!==lanes?31-clz32(lanes):0}
function clz32(x){return 0===x?32:31-Math.floor(Math.log(x)/Math.LN2)|0}var TotalLanes=31,NoLanes=0,NoLane=0,SyncLane=1,InputContinuousHydrationLane=2,InputContinuousLane=4,DefaultHydrationLane=8,DefaultLane=16,TransitionHydrationLane=32,TransitionLane=64,RetryLane=128,SelectiveHydrationLane=256,IdleHydrationLane=512,IdleLane=1024,OffscreenLane=2048,NonIdleLanes=4095;
function getHighestPriorityLanes(lanes){switch(getHighestPriorityLane(lanes)){case 1:return SyncLane;case 2:return InputContinuousHydrationLane;case 4:return InputContinuousLane;case 8:return DefaultHydrationLane;case 16:return DefaultLane;case 32:return TransitionHydrationLane;case 64:return TransitionLane;case 128:return RetryLane;case 256:return SelectiveHydrationLane;case 512:return IdleHydrationLane;case 1024:return IdleLane;case 2048:return OffscreenLane;default:return lanes}}
function getNextLanes(root,wipLanes){var pendingLanes=root.pendingLanes;if(0===pendingLanes)return NoLanes;var nextLanes=NoLanes,suspendedLanes=root.suspendedLanes,pingedLanes=root.pingedLanes;if(0===(pendingLanes&NonIdleLanes))nextLanes=pendingLanes;else{var nonIdlePendingLanes=pendingLanes&NonIdleLanes;0!==nonIdlePendingLanes?0!==(nonIdlePendingLanes&suspendedLanes)?nextLanes=getHighestPriorityLanes(nonIdlePendingLanes&pingedLanes):nextLanes=getHighestPriorityLanes(nonIdlePendingLanes):
nextLanes=getHighestPriorityLanes(pendingLanes)}if(0===nextLanes)return NoLanes;if(0!==wipLanes&&wipLanes!==nextLanes&&0===(wipLanes&suspendedLanes)){var higherPriority=getHighestPriorityLane(wipLanes),nextLanePriority=getHighestPriorityLane(nextLanes);if(nextLanePriority>higherPriority||nextLanePriority===higherPriority&&nextLanes>wipLanes)return wipLanes}0!==(nextLanes&InputContinuousLane)&&console.log("getNextLanes: "+nextLanes);return nextLanes}
function getHighestPriorityLane(lanes){return lanes&-lanes}
function includesNonIdleWork(lanes){return 0!==(lanes&NonIdleLanes)}function includesOnlyRetries(lanes){return 0===(lanes&4031)}
function includesOnlyTransitions(lanes){return 0===(lanes&4030)}
function computeExpirationTime(lane,currentTime){switch(lane){case 1:return currentTime+250;case 2:return currentTime+250;case 4:return currentTime+250;case 8:return currentTime+5e3;case 16:return currentTime+5e3;case 32:return currentTime+1e4;case 64:return currentTime+1e4;case 128:return currentTime+1e4;case 256:return-1;case 512:return-1;case 1024:return-1;case 2048:return-1;default:return-1}}
function createLaneMap(initial){for(var laneMap=[],i=0;i<TotalLanes;i++)laneMap.push(initial);return laneMap}var ReactCurrentRenderer={current:null},ReactCurrentFiber={current:null};function isRootDehydrated(root){var currentState=root.current.memoizedState;return null!==currentState.dehydrated}function getSuspendedLanes(root,wipLanes){var suspendedLanes=NoLanes;0!==(wipLanes&TransitionLane)&&(suspendedLanes|=TransitionLane);return suspendedLanes}
function markRootSuspended(root,suspendedLanes){root.suspendedLanes|=suspendedLanes;0!==(suspendedLanes&TransitionLane)&&(root.pendingLanes|=TransitionLane)}
function markRootFinished(root,remainingLanes){root.pendingLanes&=(~remainingLanes);root.suspendedLanes&=(~remainingLanes);root.pingedLanes&=(~remainingLanes);root.expiredLanes&=(~remainingLanes);root.mutableReadLanes&=(~remainingLanes);root.finishedLanes|=remainingLanes;root.finishedWork=null}
function markCommitTimeOfFallback(){globalMostRecentFallbackTime=now()}
function isAlreadyFailedLegacyErrorBoundary(instance){return null!==legacyErrorBoundaries&&legacyErrorBoundaries.has(instance)}
function isDevToolsPresent(){return"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__}
function logCommitStarted(lanes){var id=commitCounter++;if(isDevToolsPresent()){var devTools=__REACT_DEVTOOLS_GLOBAL_HOOK__;devTools.emit("commitStarted",id,lanes)}}
function logCommitStopped(){if(isDevToolsPresent()){var id=commitCounter;var devTools=__REACT_DEVTOOLS_GLOBAL_HOOK__;devTools.emit("commitStopped",id)}}var PossiblyWeakMap="function"==typeof WeakMap?WeakMap:Map;
function createRootErrorUpdate(fiber,errorInfo,lane){var update=createUpdate(NoLane);update.tag=3;update.payload={element:null};var error=new Error("Error: An error was thrown inside one of your components, but React doesn't know what it is. This is likely due to browser extensions that interfere with debugging.");update.callback=function(){onUncaughtError(error);logError(fiber,errorInfo)};update.next=null;return update}
function createClassErrorUpdate(fiber,errorInfo,lane){var update=createUpdate(lane);update.tag=3;var getDerivedStateFromError=fiber.type.getDerivedStateFromError;if("function"==typeof getDerivedStateFromError){var error=errorInfo.value;update.payload=function(){return getDerivedStateFromError(error)};update.callback=function(){logError(fiber,errorInfo)}}var inst=fiber.stateNode;null!==inst&&"function"==typeof inst.componentDidCatch&&
(update.callback=function(){logError(fiber,errorInfo);"function"!=typeof getDerivedStateFromError&&(null===legacyErrorBoundaries?legacyErrorBoundaries=new Set([this]):legacyErrorBoundaries.add(this));var error=errorInfo.value,stack=errorInfo.stack;this.componentDidCatch(error,stack)});return update}
function logError(boundary,errorInfo){var source=errorInfo.source,stack=errorInfo.stack;errorInfo=errorInfo.value;null!==boundary&&2===boundary.tag&&null!==boundary.stateNode&&"function"==typeof boundary.stateNode.componentDidCatch&&isAlreadyFailedLegacyErrorBoundary(boundary.stateNode)||onUncaughtError(errorInfo)}
function attachPingListener(root,wakeable,lanes){var pingCache=root.pingCache;if(null===pingCache){pingCache=root.pingCache=new PossiblyWeakMap;var threadIDs=new Set;pingCache.set(wakeable,threadIDs)}else threadIDs=pingCache.get(wakeable);threadIDs.has(lanes)||(threadIDs.add(lanes),root=pingSuspendedRoot.bind(null,root,wakeable,lanes),wakeable.then(root,root))}
function isSuspenseBoundaryBeingHidden(current,workInProgress){return null!==current&&(null===current.memoizedState||null!==current.memoizedState.dehydrated)&&null!==workInProgress.memoizedState&&null===workInProgress.memoizedState.dehydrated}var currentUpdate=null,currentQueue=null,didScheduleRenderPhaseUpdate=!1,didScheduleRenderPhaseUpdateDuringThisPass=!1;function throwDoubleError(a,b){throw b;}
function applyDerivedStateFromProps(workInProgress,ctor,getDerivedStateFromProps,nextProps){var prevState=workInProgress.memoizedState,partialState=getDerivedStateFromProps(nextProps,prevState);workInProgress.memoizedState=null===partialState||void 0===partialState?prevState:assign({},prevState,partialState);0===workInProgress.lanes&&(workInProgress.flags|=1)}
var hasForceUpdate=new Set;
function checkHasForceUpdateAfterProcessing(){0<hasForceUpdate.size&&(hasForceUpdate.forEach(function(warn){console.error("An update was scheduled from inside an update function. Update functions should be pure, without side effects. Consider using componentDidUpdate or a callback.")}),hasForceUpdate.clear())}
function commitUpdate(finishedWork){var flags=finishedWork.flags;0!==(flags&128)&&console.error("Should not have passive effects on clean trees");if(0!==(flags&4)){var instance=finishedWork.stateNode;"function"==typeof instance.componentDidUpdate?instance.componentDidUpdate():console.log("commitUpdate: "+flags)}if(0!==(flags&512))console.log("commitUpdate: "+flags)}
function safelyCallCommitHook(current,nearestMountedAncestor,hook,key,val){hook=current.memoizedState;hook=null===hook?null:hook[key];if(null!==hook)try{hook(val)}catch(error){captureCommitPhaseError(nearestMountedAncestor,nearestMountedAncestor.return,error)}}var emptyContextObject={},didWarnAboutMismatchedHooksForComponent;function is(x,y){return x===y&&0!==x||x!=x&&y!=y}
var currentlyRenderingFiber=null,workInProgressHook=null,firstWorkInProgressHook=null,isReRender=!1,renderLanes=NoLanes,didScheduleUpdateInCurrentRender=!1;function throwInvalidHookError(){throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for hints.");
}
function areHookInputsEqual(nextDeps,prevDeps){if(null===prevDeps)return!1;for(var i=0;i<prevDeps.length&&i<nextDeps.length;i++)if(!is$1(nextDeps[i],prevDeps[i]))return!1;return!0}
function createFunctionComponentUpdateQueue(){return{lastEffect:null,stores:null}}function basicStateReducer(state,action){return"function"==typeof action?action(state):action}
function mountWorkInProgressHook(){var hook={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};null===workInProgressHook?firstWorkInProgressHook=workInProgressHook=hook:workInProgressHook=workInProgressHook.next=hook;return workInProgressHook}
function updateWorkInProgressHook(){if(null!==workInProgressHook)var hook=workInProgressHook.next;else{hook=currentlyRenderingFiber.memoizedState;if(null===hook)return workInProgressHook=firstWorkInProgressHook,hook=workInProgressHook.next={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null},workInProgressHook;workInProgressHook=firstWorkInProgressHook;hook=workInProgressHook.next=hook}return hook}
function useTransition(){var state=useReducer(function(state,action){return!0},!1);state=state[0];var start=useCallback(function(callback,options){0===(getWorkInProgressRootRenderLanes()&TransitionLane)||console.error("startTransition cannot be called during a render. Try wrapping it in useEffect or an event handler.");var prevTransition=Internals.ReactCurrentBatchConfig.transition;Internals.ReactCurrentBatchConfig.transition={};try{state||callback()}finally{Internals.ReactCurrentBatchConfig.transition=
prevTransition}});return[state,start]}
function useDeferredValue(value,initialValue){return useMemo(function(){return value},[value])}
function mountState(initialState){var hook=mountWorkInProgressHook();"function"==typeof initialState&&(initialState=initialState());hook.memoizedState=hook.baseState=initialState;var queue=hook.queue={pending:null,lanes:NoLanes,dispatch:null,lastRenderedReducer:basicStateReducer,lastRenderedState:initialState};queue=queue.dispatch=dispatchAction.bind(null,currentlyRenderingFiber,queue);return[hook.memoizedState,queue]}
function updateState(initialState){return updateReducer(basicStateReducer)}
function rerenderState(initialState){return rerenderReducer(basicStateReducer)}
function mountReducer(reducer,initialArg,init){var hook=mountWorkInProgressHook();var initialState;void 0!==init?initialState=init(initialArg):(initialState=initialArg,reducer(initialArg,""));hook.memoizedState=hook.baseState=initialState;initialArg=hook.queue={pending:null,lanes:NoLanes,dispatch:null,lastRenderedReducer:reducer,lastRenderedState:initialState};initialArg=initialArg.dispatch=dispatchAction.bind(null,currentlyRenderingFiber,initialArg);
return[hook.memoizedState,initialArg]}
function updateReducer(reducer,initialArg,init){var hook=updateWorkInProgressHook(),queue=hook.queue;if(null===queue)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");queue.lastRenderedReducer=reducer;var current=workInProgressHook,baseQueue=current.baseQueue,pendingQueue=queue.pending;if(null!==pendingQueue){if(null!==baseQueue){var baseFirst=baseQueue.next,pendingFirst=pendingQueue.next;baseQueue.next=pendingFirst;pendingQueue.next=
baseFirst}current.baseQueue=baseQueue=pendingQueue;queue.pending=null}if(null!==baseQueue){pendingQueue=baseQueue.next;current=current.baseState;var newBaseState=null,newBaseQueueFirst=null,newBaseQueueLast=null,update=pendingQueue;do{var updateLane=update.lane;if(!isSubsetOfLanes(renderLanes,updateLane)){var clone=assign({},update);clone.next=null;null===newBaseQueueLast?newBaseQueueFirst=newBaseQueueLast=clone:newBaseQueueLast=newBaseQueueLast.next=clone;current=
current}else{null!==newBaseQueueLast&&(clone=assign({},update),clone.next=null,newBaseQueueLast=newBaseQueueLast.next=clone);var action=update.action;update=current;if(didScheduleRenderPhaseUpdateDuringThisPass)throwDoubleError();current=reducer(current,action)}update=update.next}while(null!==update&&update!==pendingQueue);null===newBaseQueueLast?newBaseState=current:newBaseQueueLast.next=newBaseQueueFirst;if(!is$1(current,hook.memoizedState))markWorkInProgressReceivedUpdate();
hook.memoizedState=current;hook.baseState=newBaseState;hook.baseQueue=newBaseQueueLast;queue.lastRenderedState=current}else if(null===queue.pending)return[hook.memoizedState,queue.dispatch];if(reducer!==queue.lastRenderedReducer)throw Error("The final state from dispatch differs from what React expected. This may be caused by copying a dispatch function, because React keeps track of the reducer associated with each dispatch.");return[hook.memo-izedState,queue.dispatch]}
function rerenderReducer(reducer,initialArg,init){var hook=updateWorkInProgressHook(),queue=hook.queue;if(null===queue)throw Error("Should have a queue. This is likely a bug in React. Please file an issue.");queue.lastRenderedReducer=reducer;var dispatch=queue.dispatch,lastRenderedState=queue.lastRenderedState,eagerState=lastRenderedState;var pending=queue.pending;if(null!==pending){var first=pending.next,update=first;do{var updateLane=update.lane;isSubsetOfLanes(renderLanes,
updateLane)?eagerState=reducer(eagerState,update.action):console.log("rerenderReducer: "+updateLane);update=update.next}while(update!==first)}if(!is$1(eagerState,lastRenderedState))return markWorkInProgressReceivedUpdate(),hook.memoizedState=eagerState,queue.lastRenderedState=eagerState,[eagerState,dispatch];return[lastRenderedState,dispatch]}
function readContext(context){return context._currentValue}
function mountRef(initialValue){var hook=mountWorkInProgressHook();var ref={current:initialValue};return hook.memoizedState=ref}function updateRef(initialValue){var hook=updateWorkInProgressHook();return hook.memoizedState}
function mountEffect(create,deps){return mountEffectImpl(516,4,create,deps)}
function updateEffect(create,deps){return updateEffectImpl(516,4,create,deps)}
function mountLayoutEffect(create,deps){return mountEffectImpl(4,2,create,deps)}
function updateLayoutEffect(create,deps){return updateEffectImpl(4,2,create,deps)}
function imperativeHandleEffect(create,ref){if("function"==typeof ref)return create=create(),ref(create),function(){ref(null)};if(null!=ref)return create=create(),ref.current=create,function(){ref.current=null}}
function mountImperativeHandle(ref,create,deps){var effectDeps=null!=deps?deps.concat([ref]):null;return mountEffectImpl(8,4,imperativeHandleEffect.bind(null,create,ref),effectDeps)}
function updateImperativeHandle(ref,create,deps){var effectDeps=null!=deps?deps.concat([ref]):null;return updateEffectImpl(8,4,imperativeHandleEffect.bind(null,create,ref),effectDeps)}
function mountCallback(callback,deps){var hook=mountWorkInProgressHook();deps=void 0===deps?null:deps;hook.memoizedState=[callback,deps];return callback}
function updateCallback(callback,deps){var hook=updateWorkInProgressHook();deps=void 0===deps?null:deps;var prevState=hook.memoizedState;if(null!==prevState&&null!==deps){var prevDeps=prevState[1];if(areHookInputsEqual(deps,prevDeps))return prevState[0]}hook.memoizedState=[callback,deps];return callback}
function mountMemo(nextCreate,deps){var hook=mountWorkInProgressHook();deps=void 0===deps?null:deps;var nextValue=nextCreate();hook.memoizedState=[nextValue,deps];return nextValue}
function updateMemo(nextCreate,deps){var hook=updateWorkInProgressHook();deps=void 0===deps?null:deps;var prevState=hook.memoizedState;if(null!==prevState&&null!==deps){var prevDeps=prevState[1];if(areHookInputsEqual(deps,prevDeps))return prevState[0]}var nextValue=nextCreate();hook.memoizedState=[nextValue,deps];return nextValue}
function mountDeferredValue(value,initialValue){var hook=mountWorkInProgressHook();initialValue=useDeferredValue(value,void 0);hook.memoizedState=initialValue;return initialValue}
function updateDeferredValue(value,initialValue){var hook=updateWorkInProgressHook();var resolvedValue=useDeferredValue(value,void 0);var prevValue=hook.memoizedState;return is$1(resolvedValue,prevValue)?prevValue:(markWorkInProgressReceivedUpdate(),hook.memoizedState=resolvedValue,resolvedValue)}
function rerenderDeferredValue(value,initialValue){var hook=updateWorkInProgressHook();var resolvedValue=useDeferredValue(value,void 0);var prevValue=hook.memoizedState;return is$1(resolvedValue,prevValue)?prevValue:resolvedValue}
function startTransition(setPending,callback,options){var priority=getCurrentPriorityLevel();if(priority>UserBlockingPriority)switch(priority){case NormalPriority:case LowPriority:priority=NormalPriority;break;default:priority=UserBlockingPriority}else priority=UserBlockingPriority;var prevTransition=Internals.ReactCurrentBatchConfig.transition;Internals.ReactCurrentBatchConfig.transition={};try{setPending(!0),callback()}finally{Internals.ReactCurrentBatchConfig.transition=
prevTransition}}
function mountTransition(){var isPendingRef={isPending:!1},start=startTransition.bind(null,function(isPending){isPendingRef.isPending=isPending});mountRef(isPendingRef);mountCallback(start);return[isPendingRef.isPending,start]}
function updateTransition(){var isPendingRef=updateRef().current;var start=updateCallback(startTransition.bind(null,function(isPending){isPendingRef.isPending=isPending}));return[isPendingRef.isPending,start]}
function rerenderTransition(){var isPendingRef=updateRef().current;var start=updateCallback(startTransition.bind(null,function(isPending){isPendingRef.isPending=isPending}));return[isPendingRef.isPending,start]}
function mountId(){var hook=mountWorkInProgressHook();var id=nextReactIdCounter++;return hook.memoizedState=":"+rendererSigil+":"+id.toString(36)+":"}function updateId(){var hook=updateWorkInProgressHook();return hook.memoizedState}
function mountSyncExternalStore(subscribe,getSnapshot,getServerSnapshot){var hook=mountWorkInProgressHook();var value=getSnapshot();var inst={value,getSnapshot};hook.memoizedState=inst;var store={value,getSnapshot:getSnapshot,subscribe:subscribe,inst:inst};hook.queue=store;mountEffect(function(){inst.value=value;inst.getSnapshot=getSnapshot;if(checkIfSnapshotChanged(inst))markWorkInProgressReceivedUpdate();return subscribe(function(){checkIfSnapshotChanged(inst)&&
markWorkInProgressReceivedUpdate()})},[subscribe]);return value}
function updateSyncExternalStore(subscribe,getSnapshot,getServerSnapshot){var hook=updateWorkInProgressHook();var inst=hook.queue;var value=getSnapshot();if(checkIfSnapshotChanged(inst.inst))return markWorkInProgressReceivedUpdate(),getSnapshot();return value}
function rerenderSyncExternalStore(subscribe,getSnapshot,getServerSnapshot){var hook=updateWorkInProgressHook();return hook.queue.getSnapshot()}
function mountInsertionEffect(create,deps){return mountEffectImpl(2052,8,create,deps)}
function updateInsertionEffect(create,deps){return updateEffectImpl(2052,8,create,deps)}
function mountEffectImpl(fiberFlags,hookFlags,create,deps){var hook=mountWorkInProgressHook();var nextDeps=void 0===deps?null:deps;var destroy=void 0;workInProgressHook.memoizedState={create,destroy,deps:nextDeps,next:null};currentlyRenderingFiber.flags|=fiberFlags;firstWorkInProgressHook.lastEffect={tag:hookFlags,create,destroy:void 0,deps:nextDeps,next:null}}
function updateEffectImpl(fiberFlags,hookFlags,create,deps){var hook=updateWorkInProgressHook(),nextDeps=void 0===deps?null:deps,destroy=void 0;if(null!==workInProgressHook){var prevEffect=workInProgressHook.memoizedState;destroy=prevEffect.destroy;if(null!==nextDeps){var prevDeps=prevEffect.deps;if(areHookInputsEqual(nextDeps,prevDeps)){hook.memoizedState={create:prevEffect.create,destroy,deps:prevDeps,next:null};return}}}currentlyRenderingFiber.flags|=fiberFlags;hook.memoizedState=
{create,destroy,deps:nextDeps,next:null};firstWorkInProgressHook.lastEffect={tag:hookFlags,create,destroy:void 0,deps:nextDeps,next:null}}
function checkIfSnapshotChanged(inst){var latestGetSnapshot=inst.getSnapshot,prevValue=inst.value;try{var nextValue=latestGetSnapshot();return!is$1(prevValue,nextValue)}catch(error){return!0}}
function dispatchAction(fiber,queue,action){var eventTime=requestEventTime(),lane=requestUpdateLane(fiber);var update={lane,action,hasEagerState:!1,eagerState:null,next:null};if(isRenderPhaseUpdate(fiber))enqueueRenderPhaseUpdate(queue,update);else{var root=enqueueConcurrentHookUpdate(fiber,queue,update,lane);if(null!==root){update=isRootDehydrated(root);var isUpdateInHydrationStage=0!==(lane&DefaultHydrationLane);if(!update||!isUpdateInHydrationStage)isUpdateInHydrationStage=
getWorkInProgressRoot();null!==isUpdateInHydrationStage&&scheduleUpdateOnFiber(root,fiber,lane,eventTime)}}}
function isRenderPhaseUpdate(fiber){var alternate=fiber.alternate;return fiber===currentlyRenderingFiber||null!==alternate&&alternate===currentlyRenderingFiber}
function enqueueRenderPhaseUpdate(queue,update){didScheduleRenderPhaseUpdateDuringThisPass=didScheduleRenderPhaseUpdate=!0;var pending=queue.pending;null===pending?update.next=update:(update.next=pending.next,pending.next=update);queue.pending=update}
function renderDidSuspend(){null!==workInProgressRoot&&workInProgressRoot.tag,workInProgressRoot.memoizedState.pingCache}var ReactCurrentDispatcher$1=Internals.ReactCurrentDispatcher;function resolveDispatcher(){var dispatcher=ReactCurrentDispatcher$1.current;if(null===dispatcher)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for hints.");
return dispatcher}
function useContext(context){return resolveDispatcher().useContext(context)}function useState(initialState){return resolveDispatcher().useState(initialState)}function useReducer(reducer,initialArg,init){return resolveDispatcher().useReducer(reducer,initialArg,init)}function useRef(initialValue){return resolveDispatcher().useRef(initialValue)}function useEffect(create,deps){return resolveDispatcher().useEffect(create,deps)}
function useInsertionEffect(create,deps){return resolveDispatcher().useInsertionEffect(create,deps)}
function useLayoutEffect(create,deps){return resolveDispatcher().useLayoutEffect(create,deps)}function useCallback(callback,deps){return resolveDispatcher().useCallback(callback,deps)}function useMemo(create,deps){return resolveDispatcher().useMemo(create,deps)}function useImperativeHandle(ref,create,deps){return resolveDispatcher().useImperativeHandle(ref,create,deps)}
function useDebugValue(value,formatterFn){}function useId(){return resolveDispatcher().useId()}function useSyncExternalStore(subscribe,getSnapshot,getServerSnapshot){return resolveDispatcher().useSyncExternalStore(subscribe,getSnapshot,getServerSnapshot)}
function createUpdate(lane){return{eventTime:0,lane:lane,tag:0,payload:null,callback:null,next:null}}function createFiber(tag,pendingProps,key,mode){return{tag,key,elementType:null,type:null,stateNode:null,return:null,child:null,sibling:null,index:0,ref:null,pendingProps,memoizedProps:null,updateQueue:null,memoizedState:null,dependencies:null,mode,flags:0,subtreeFlags:0,deletions:null,lanes:NoLanes,childLanes:NoLanes,alternate:null}}
function createHostRootFiber(tag,isStrictMode,concurrentUpdatesByDefaultOverride,identifierPrefix,onRecoverableError){return createFiber(3,{current:null,containerInfo:{},pendingChildren:null,implementation:null,pendingLanes:NoLanes,suspendedLanes:NoLanes,pingedLanes:NoLanes,expiredLanes:NoLanes,mutableReadLanes:NoLanes,finishedLanes:NoLanes,errorRecoveryDisabled:!1,finishedWork:null,timeoutHandle:-1,context:null,pendingContext:null,hydrate:!1,callbackNode:null,callbackPriority:0,
eventTimes:createLaneMap(-1),expirationTimes:createLaneMap(-1),pendingCounts:createLaneMap(0),suspendedCounts:createLaneMap(0),pingedCounts:createLaneMap(0),expiredCounts:createLaneMap(0),mutableSourceEagerHydrationData:null},null,0)}
function createFiberFromFragment(elements,mode,lanes,key){elements=createFiber(7,elements,key,mode);elements.lanes=lanes;return elements}
function createFiberFromOffscreen(pendingProps,mode,lanes,key){pendingProps=createFiber(22,pendingProps,key,mode);pendingProps.lanes=lanes;pendingProps.childLanes=lanes;return pendingProps}
function createWorkInProgress(current,pendingProps){var workInProgress=current.alternate;null===workInProgress?(workInProgress=createFiber(current.tag,pendingProps,current.key,current.mode),workInProgress.elementType=current.elementType,workInProgress.type=current.type,workInProgress.stateNode=current.stateNode,workInProgress.alternate=current,current.alternate=workInProgress):(workInProgress.pendingProps=pendingProps,workInProgress.type=current.type,workInProgress.flags=
0,workInProgress.subtreeFlags=0,workInProgress.deletions=null);workInProgress.flags=current.flags&14680064;workInProgress.childLanes=current.childLanes;workInProgress.lanes=current.lanes;workInProgress.child=current.child;workInProgress.memoizedProps=current.memoizedProps;workInProgress.memoizedState=current.memoizedState;workInProgress.updateQueue=current.updateQueue;pendingProps=current.dependencies;workInProgress.dependencies=null===pendingProps?null:{lanes:pendingProps.lanes,
firstContext:pendingProps.firstContext};workInProgress.sibling=current.sibling;workInProgress.index=current.index;workInProgress.ref=current.ref;return workInProgress}
function createFiberFromTypeAndProps(type,key,pendingProps,owner,mode,lanes){var fiberTag=2;var resolvedType=type;if("function"==typeof type){if(shouldConstruct(type))fiberTag=1}else if("string"==typeof type)fiberTag=5;else a:switch(type){case REACT_FRAGMENT_TYPE:return createFiberFromFragment(pendingProps.children,mode,lanes,key);case REACT_STRICT_MODE_TYPE:fiberTag=8;mode|=8;break;case REACT_PROFILER_TYPE:return type=createFiber(12,pendingProps,key,mode|4),type.elementType=
REACT_PROFILER_TYPE,type.lanes=lanes,type;case REACT_SUSPENSE_TYPE:return type=createFiber(13,pendingProps,key,mode),type.elementType=REACT_SUSPENSE_TYPE,type.lanes=lanes,type;case REACT_SUSPENSE_LIST_TYPE:return type=createFiber(19,pendingProps,key,mode),type.elementType=REACT_SUSPENSE_LIST_TYPE,type.lanes=lanes,type;case REACT_OFFSCREEN_TYPE:return createFiberFromOffscreen(pendingProps,mode,lanes,key);case "devtools":return type=createFiber(26,pendingProps,key,mode),type.elementType=
"devtools",type.lanes=lanes,type;default:if("object"==typeof type&&null!==type)switch(type.$$typeof){case REACT_PROVIDER_TYPE:fiberTag=10;break a;case REACT_CONTEXT_TYPE:fiberTag=9;break a;case REACT_FORWARD_REF_TYPE:fiberTag=11;break a;case REACT_MEMO_TYPE:fiberTag=14;break a;case REACT_LAZY_TYPE:fiberTag=16;resolvedType=null;break a}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+(null==type?
type:typeof type)+".");}pendingProps=createFiber(fiberTag,pendingProps,key,mode);pendingProps.elementType=type;pendingProps.type=resolvedType;pendingProps.lanes=lanes;return pendingProps}function shouldConstruct(Component){return!!Component.prototype&&!!Component.prototype.isReactComponent}
function isSimpleFunctionComponent(type){return"function"==typeof type&&!shouldConstruct(type)&&null===type.defaultProps}
function resolveLazy(lazyType){var payload=lazyType._payload,result=lazyType._init;return result(payload)}
var hasBadMapPolyfill,hasBadSetPolyfill;if("function"==typeof Map)try{new Map([[1,2]]).keys().next().done||(hasBadMapPolyfill=!0)}catch(e){}if("function"==typeof Set)try{new Set([1]).keys().next().done||(hasBadSetPolyfill=!0)}catch(e){}var isArray=Array.isArray;
function bailoutHooks(current,workInProgress,lanes){workInProgress.updateQueue=current.updateQueue;workInProgress.flags&=-2053;current.lanes&=~lanes}function warnOnInvalidKey(value,componentName){"number"==typeof value&&console.warn("Number keys are not supported. Use strings instead.")}
function warnOnMissingKey(child,componentName,isCurrentRenderer,fiber){"object"==typeof child&&null!==child&&(child.$$typeof===REACT_ELEMENT_TYPE&&null===child.key&&child._owner&&isCurrentRenderer(child._owner,fiber)&&console.warn("Each child in a list should have a unique 'key' prop. See https://reactjs.org/link/warning-keys for more information."))}
function reconcileChildFibers(returnFiber,currentFirstChild,newChild,lanes){var isObject=!1;if("object"==typeof newChild&&null!==newChild){switch(newChild.$$typeof){case REACT_ELEMENT_TYPE:a:if(isObject=!0,!newChild.key)var key=null;else key=""+newChild.key,key=null;break a;case REACT_PORTAL_TYPE:isObject=!0;key=null;break a}key=getIteratorFn(newChild)}if(!isObject){if("string"==typeof newChild||"number"==typeof newChild)return newChild=""+newChild,null!==currentFirstChild&&
6===currentFirstChild.tag?(deleteRemainingChildren(returnFiber,currentFirstChild.sibling),currentFirstChild=reuseTextWork(currentFirstChild,newChild),currentFirstChild.return=returnFiber,currentFirstChild):(deleteRemainingChildren(returnFiber,currentFirstChild),newChild=createFiberFromText(newChild,returnFiber.mode,lanes),newChild.return=returnFiber,newChild);if(isArray(newChild))return null!==currentFirstChild&&4===currentFirstChild.tag?(key=currentFirstChild.key,deleteRemainingChildren(returnFiber,
currentFirstChild.sibling),currentFirstChild=reuseFragment(currentFirstChild,newChild,key),currentFirstChild.return=returnFiber,currentFirstChild):(deleteRemainingChildren(returnFiber,currentFirstChild),newChild=createFiberFromFragment(newChild,returnFiber.mode,lanes,null),newChild.return=returnFiber,newChild)}for(key=null!==currentFirstChild?currentFirstChild.key:null;null!==currentFirstChild;){if(currentFirstChild.key===key)if(4===currentFirstChild.tag){if(7===newChild.tag)return deleteRemainingChildren(returnFiber,
currentFirstChild.sibling),key=reuseFragment(currentFirstChild,newChild.props.children,newChild.key),key.return=returnFiber,key;deleteChild(returnFiber,currentFirstChild)}else if(currentFirstChild.elementType===newChild.type)return deleteRemainingChildren(returnFiber,currentFirstChild.sibling),key=reuseWork(currentFirstChild,newChild.props),key.ref=coerceRef(returnFiber,currentFirstChild,newChild),key.return=returnFiber,key;else deleteChild(returnFiber,currentFirstChild);
else deleteChild(returnFiber,currentFirstChild);currentFirstChild=currentFirstChild.sibling}}
function reconcileSingleElement(returnFiber,currentFirstChild,element,lanes){var key=element.key,child=currentFirstChild;for(;null!==child;){if(child.key===key){if(4===child.tag){if(7===element.type)return deleteRemainingChildren(returnFiber,child.sibling),key=reuseFragment(child,element.props.children,key),key.return=returnFiber,key;break}else if(child.elementType===element.type)return deleteRemainingChildren(returnFiber,child.sibling),key=reuseWork(child,element.props),
key.ref=coerceRef(returnFiber,child,element),key.return=returnFiber,key;break}else deleteChild(returnFiber,child);child=child.sibling}if(7===element.type)return key=createFiberFromFragment(element.props.children,returnFiber.mode,lanes,element.key),key.return=returnFiber,key;element=createFiberFromTypeAndProps(element.type,element.key,element.props,null,returnFiber.mode,lanes);element.ref=coerceRef(returnFiber,currentFirstChild,element);element.return=returnFiber;
return element}
function reconcileSingleTextNode(returnFiber,currentFirstChild,textContent,lanes){if(null!==currentFirstChild&&6===currentFirstChild.tag)return deleteRemainingChildren(returnFiber,currentFirstChild.sibling),textContent=reuseTextWork(currentFirstChild,textContent),textContent.return=returnFiber,textContent;deleteRemainingChildren(returnFiber,currentFirstChild);textContent=createFiberFromText(textContent,returnFiber.mode,lanes);textContent.return=returnFiber;return textContent}
function reconcileChildrenArray(returnFiber,currentFirstChild,newChildren,lanes){var resultingFirstChild=null,previousNewFiber=null,oldFiber=currentFirstChild,lastPlacedIndex=0,newIdx=0,nextOldFiber=null;for(;null!==oldFiber&&newIdx<newChildren.length;newIdx++){oldFiber.index>newIdx?(nextOldFiber=oldFiber,oldFiber=null):nextOldFiber=oldFiber.sibling;var newFiber=updateSlot(returnFiber,oldFiber,newChildren[newIdx],lanes);if(null===newFiber)break;null!==oldFiber&&(null===nextOldFiber?
console.error("Should have next old fiber"):oldFiber=nextOldFiber);lastPlacedIndex=placeChild(newFiber,lastPlacedIndex,newIdx);null===previousNewFiber?resultingFirstChild=newFiber:previousNewFiber.sibling=newFiber;previousNewFiber=newFiber}if(newIdx===newChildren.length)return deleteRemainingChildren(returnFiber,oldFiber),resultingFirstChild;if(null===oldFiber){for(;newIdx<newChildren.length;newIdx++)newFiber=createChild(returnFiber,newChildren[newIdx],lanes),null!==newFiber&&
(lastPlacedIndex=placeChild(newFiber,lastPlacedIndex,newIdx),null===previousNewFiber?resultingFirstChild=newFiber:previousNewFiber.sibling=newFiber,previousNewFiber=newFiber);return resultingFirstChild}for(oldFiber=mapRemainingChildren(returnFiber,oldFiber);newIdx<newChildren.length;newIdx++)newFiber=updateFromMap(oldFiber,returnFiber,newIdx,newChildren[newIdx],lanes),null!==newFiber&&(oldFiber.delete(null===newFiber.key?newIdx:newFiber.key),lastPlacedIndex=placeChild(newFiber,
lastPlacedIndex,newIdx),null===previousNewFiber?resultingFirstChild=newFiber:previousNewFiber.sibling=newFiber,previousNewFiber=newFiber);oldFiber.forEach(function(child){return deleteChild(returnFiber,child)});return resultingFirstChild}
function reconcileChildrenIterator(returnFiber,currentFirstChild,newChildren,lanes){var iteratorFn=getIteratorFn(newChildren);if("function"!=typeof iteratorFn)throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");newChildren=iteratorFn.call(newChildren);var resultingFirstChild=null,previousNewFiber=null,oldFiber=currentFirstChild,lastPlacedIndex=0,newIdx=0,nextOldFiber=null,step=newChildren.next();for(;null!==oldFiber&&
!step.done;newIdx++,step=newChildren.next()){oldFiber.index>newIdx?(nextOldFiber=oldFiber,oldFiber=null):nextOldFiber=oldFiber.sibling;var newFiber=updateSlot(returnFiber,oldFiber,step.value,lanes);if(null===newFiber)break;null!==oldFiber&&(null===nextOldFiber?console.error("Should have next old fiber"):oldFiber=nextOldFiber);lastPlacedIndex=placeChild(newFiber,lastPlacedIndex,newIdx);null===previousNewFiber?resultingFirstChild=newFiber:previousNewFiber.sibling=newFiber;
previousNewFiber=newFiber}if(step.done)return deleteRemainingChildren(returnFiber,oldFiber),resultingFirstChild;if(null===oldFiber){for(;!step.done;newIdx++,step=newChildren.next())step=createChild(returnFiber,step.value,lanes),null!==step&&(lastPlacedIndex=placeChild(step,lastPlacedIndex,newIdx),null===previousNewFiber?resultingFirstChild=step:previousNewFiber.sibling=step,previousNewFiber=step);return resultingFirstChild}for(oldFiber=mapRemainingChildren(returnFiber,oldFiber);!step.done;newIdx++,
step=newChildren.next())step=updateFromMap(oldFiber,returnFiber,newIdx,step.value,lanes),null!==step&&(oldFiber.delete(null===step.key?newIdx:step.key),lastPlacedIndex=placeChild(step,lastPlacedIndex,newIdx),null===previousNewFiber?resultingFirstChild=step:previousNewFiber.sibling=step,previousNewFiber=step);oldFiber.forEach(function(child){return deleteChild(returnFiber,child)});return resultingFirstChild}
function reconcileChildren(returnFiber,currentFirstChild,newChild,renderLanes){var isUnkeyedTopLevelFragment=isObject(newChild)&&newChild.type===REACT_FRAGMENT_TYPE&&null===newChild.key;isUnkeyedTopLevelFragment&&(newChild=newChild.props.children);var isObject$1=!1;if("object"==typeof newChild&&null!==newChild){switch(newChild.$$typeof){case REACT_ELEMENT_TYPE:a:if(isObject$1=!0,!newChild.key)var key=null;else key=""+newChild.key,key=null;break a;case REACT_PORTAL_TYPE:isObject$1=
!0;key=null;break a}key=getIteratorFn(newChild)}if(isObject$1)return reconcileSingleElement(returnFiber,currentFirstChild,newChild,renderLanes);if("string"==typeof newChild||"number"==typeof newChild)return newChild=""+newChild,reconcileSingleTextNode(returnFiber,currentFirstChild,newChild,renderLanes);if(isArray(newChild))return reconcileChildrenArray(returnFiber,currentFirstChild,newChild,renderLanes);if(key)return reconcileChildrenIterator(returnFiber,currentFirstChild,newChild,
renderLanes);if(isUnkeyedTopLevelFragment){isObject$1=newChild;if(isArray(isObject$1))return reconcileChildrenArray(returnFiber,currentFirstChild,isObject$1,renderLanes);if(key=getIteratorFn(isObject$1))return reconcileChildrenIterator(returnFiber,currentFirstChild,isObject$1,renderLanes);deleteRemainingChildren(returnFiber,currentFirstChild)}return deleteRemainingChildren(returnFiber,currentFirstChild)}
function deleteRemainingChildren(returnFiber,currentFirstChild){for(;null!==currentFirstChild;){deleteChild(returnFiber,currentFirstChild);currentFirstChild=currentFirstChild.sibling}return null}
function mapRemainingChildren(returnFiber,currentFirstChild){for(var existingChildren=new Map;null!==currentFirstChild;)null!==currentFirstChild.key?existingChildren.set(currentFirstChild.key,currentFirstChild):existingChildren.set(currentFirstChild.index,currentFirstChild),currentFirstChild=currentFirstChild.sibling;return existingChildren}
function reuseWork(fiber,pendingProps){return cloneChild(fiber,pendingProps)}
function reuseTextWork(fiber,textContent){fiber=createWorkInProgress(fiber,textContent);fiber.flags|=2;return fiber}
function reuseFragment(fiber,pendingProps,key){fiber=createWorkInProgress(fiber,pendingProps);fiber.key=key;return fiber}
function createChild(returnFiber,newChild,lanes){if("string"==typeof newChild||"number"==typeof newChild)return newChild=""+newChild,newChild=createFiberFromText(newChild,returnFiber.mode,lanes),newChild.return=returnFiber,newChild;if("object"==typeof newChild&&null!==newChild){switch(newChild.$$typeof){case REACT_ELEMENT_TYPE:return lanes=createFiberFromTypeAndProps(newChild.type,newChild.key,newChild.props,null,returnFiber.mode,lanes),lanes.ref=coerceRef(returnFiber,
null,newChild),lanes.return=returnFiber,lanes;case REACT_PORTAL_TYPE:return newChild=createFiberFromPortal(newChild,returnFiber.mode,lanes),newChild.return=returnFiber,newChild;case REACT_LAZY_TYPE:return createChild(returnFiber,resolveLazy(newChild),lanes)}if(isArray(newChild)||(lanes=getIteratorFn(newChild)))return newChild=createFiberFromFragment(newChild,returnFiber.mode,lanes,null),newChild.return=returnFiber,newChild;throwOnInvalidObjectType(returnFiber,newChild)}return null}
function updateSlot(returnFiber,oldFiber,newChild,lanes){var key=null!==oldFiber?oldFiber.key:null;if("string"==typeof newChild||"number"==typeof newChild)return null!==key?null:(newChild=""+newChild,reuseTextWork(oldFiber,newChild));if("object"==typeof newChild&&null!==newChild){switch(newChild.$$typeof){case REACT_ELEMENT_TYPE:return newChild.key===key?7===newChild.type?reuseFragment(oldFiber,newChild.props.children,key):reuseWork(oldFiber,newChild.props):null;case REACT_PORTAL_TYPE:return newChild.key===
key?reusePortal(oldFiber,newChild.props):null;case REACT_LAZY_TYPE:return updateSlot(returnFiber,oldFiber,resolveLazy(newChild),lanes)}if(isArray(newChild)||(key=getIteratorFn(newChild)))return null!==key?null:reuseFragment(oldFiber,newChild,null);throwOnInvalidObjectType(returnFiber,newChild)}return null}
function updateFromMap(existingChildren,returnFiber,newIdx,newChild,lanes){if("string"==typeof newChild||"number"==typeof newChild)return newChild=""+newChild,existingChildren=existingChildren.get(newIdx)||null,reuseTextWork(existingChildren,newChild);if("object"==typeof newChild&&null!==newChild){switch(newChild.$$typeof){case REACT_ELEMENT_TYPE:return existingChildren=existingChildren.get(null===newChild.key?newIdx:newChild.key)||null,7===newChild.type?reuseFragment(existingChildren,
newChild.props.children,newChild.key):reuseWork(existingChildren,newChild.props);case REACT_PORTAL_TYPE:return existingChildren=existingChildren.get(null===newChild.key?newIdx:newChild.key)||null,reusePortal(existingChildren,newChild.props);case REACT_LAZY_TYPE:return updateFromMap(existingChildren,returnFiber,newIdx,resolveLazy(newChild),lanes)}if(isArray(newChild)||(lanes=getIteratorFn(newChild)))return existingChildren=existingChildren.get(newIdx)||null,reuseFragment(existingChildren,
newChild,null);throwOnInvalidObjectType(returnFiber,newChild)}return null}
function placeChild(newFiber,lastPlacedIndex,newIndex){newFiber.index=newIndex;var current=newFiber.alternate;if(null!==current)return current=current.index,current<lastPlacedIndex?(newFiber.flags|=2,lastPlacedIndex):(newFiber.flags&=-3,lastPlacedIndex);newFiber.flags|=2;return lastPlacedIndex}function createFiberFromPortal(portal,mode,lanes){mode=createFiber(4,null!==portal.key?portal.key:null,mode);mode.stateNode=portal.children;mode.return=portal;return mode}
function reusePortal(returnFiber,pendingProps){returnFiber=createWorkInProgress(returnFiber,pendingProps.children);returnFiber.flags|=2;return returnFiber}function deleteChild(returnFiber,childToDelete){var deletions=returnFiber.deletions;null===deletions?returnFiber.deletions=[childToDelete]:deletions.push(childToDelete);childToDelete.flags|=8}
function cloneChild(child,pendingProps){child=createWorkInProgress(child,pendingProps);child.flags|=2;return child}
function coerceRef(returnFiber,current,element){var ref=element.ref;if(null!==ref&&"function"!=typeof ref&&"object"!=typeof ref){if(element._owner){element=element._owner;var inst;element&&(1!==element.tag&&console.error("Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?"),inst=element.stateNode);if(!inst)throw Error("Missing owner for string ref "+ref+". This error is likely caused by a bug in React. Please file an issue.");
ref=inst.refs;if(null===ref)throw Error("Trying to access refs on an invalid instance. This error is likely caused by a bug in React. Please file an issue.");return ref[ref]}}return ref}
function throwOnInvalidObjectType(returnFiber,newChild){var childString="object"==typeof newChild?newChild.toString():""+newChild;throw Error("Objects are not valid as a React child (found: "+("[object Object]"===childString?"object with keys {"+Object.keys(newChild).join(", ")+"}":childString)+"). If you meant to render a collection of children, use an array instead.");}
var valueCursor={current:emptyContextObject},contextInst={current:null},rootInstanceStack={current:null};function pushTopLevelContextObject(fiber,context,isPrimaryRenderer){0!==(fiber.mode&16)&&isPrimaryRenderer?rootInstanceStack.current=context:rootInstanceStack.current=emptyContextObject}function scheduleContextWorkOnParentPath(parent,renderLanes){for(;null!==parent;){var alternate=parent.alternate;if(!isSubsetOfLanes(parent.childLanes,renderLanes))break;null!==alternate&&
!isSubsetOfLanes(alternate.childLanes,renderLanes)&&(alternate.childLanes|=renderLanes);parent=parent.return}if(null===parent&&null!==workInProgressRoot)throw Error("This is a bug in React. Please file an issue.");}
function findFirstUnreadContext(fiber){for(var node=fiber;null!==node;){if(9===node.tag){var current=node.memoizedProps.value;var didReadManager=node.type._context;if(is$1(current,didReadManager._currentValue))break}node=node.return}return node}
function prepareToReadContext(workInProgress,renderLanes){contextInst.current=workInProgress;valueCursor.current=emptyContextObject;var node=workInProgress.dependencies;if(null!==node)for(var firstContext=node.firstContext;null!==firstContext;){if(isSubsetOfLanes(firstContext.lanes,renderLanes))firstContext=firstContext.next;else{var context=firstContext.context;var newValue=context._currentValue;context=valueCursor.current;is$1(context,newValue)||isPrimaryRenderer(firstContext.renderer)||
scheduleContextWorkOnParentPath(workInProgress,renderLanes);valueCursor.current=newValue}firstContext=firstContext.next}return valueCursor.current}
function propagateContextChange(workInProgress,context,renderLanes){var fiber=workInProgress.child;for(null!==fiber&&(fiber.return=workInProgress);null!==fiber;){var nextFiber=void 0;var list=fiber.dependencies;if(null!==list){nextFiber=fiber.child;for(var dependency=list.firstContext;null!==dependency;){if(dependency.context===context){if(1===fiber.tag){dependency=createUpdate(renderLanes);dependency.tag=1;enqueueUpdate(fiber,dependency,renderLanes)}fiber.lanes|=
renderLanes;var alternate=fiber.alternate;null!==alternate&&(alternate.lanes|=renderLanes);scheduleContextWorkOnParentPath(fiber.return,renderLanes);list.lanes|=renderLanes;break}dependency=dependency.next}nextFiber=fiber.sibling}else if(10===fiber.tag)nextFiber=fiber.type===workInProgress.type?null:fiber.child;else if(18===fiber.tag){nextFiber=fiber.return;if(null===nextFiber)throw Error("This is a bug in React. Please file an issue.");nextFiber.lanes|=renderLanes;var _alternate=
nextFiber.alternate;null!==_alternate&&(_alternate.lanes|=renderLanes);scheduleContextWorkOnParentPath(nextFiber,renderLanes);nextFiber=fiber.sibling}else nextFiber=fiber.child;if(null!==nextFiber)nextFiber.return=fiber;else for(nextFiber=fiber;null!==nextFiber;){if(nextFiber===workInProgress)break;var sibling=nextFiber.sibling;if(null!==sibling){sibling.return=nextFiber.return;nextFiber=sibling;break}nextFiber=nextFiber.return}fiber=nextFiber}}
function trackUsedThenable(thenable){if(null===thenableState)return;var thenables=thenableState;var id=thenable._reactInternals;void 0===id?(id=thenableCounter++,thenable._reactInternals=id,thenables.set(thenable,id)):id=id;thenable=workInProgress.dependencies;null===thenable?workInProgress.dependencies={lanes:NoLanes,firstContext:null,thenables:new Map([[id,thenableState]])}:thenable.thenables.set(id,thenableState)}
function suspendIfThenableIsPending(thenable,outerWorkInProgress,renderLanes){var thenableState$jscomp$0=thenableState;if(null!==thenableState$jscomp$0){var thenableState$jscomp$0$alternate=thenableState$jscomp$0.alternate;null!==thenableState$jscomp$0$alternate?workInProgress.child=thenableState$jscomp$0$alternate:workInProgress.child=thenableState$jscomp$0}}
function pushPrimaryTreeSuspenseHandler(handler){var previousSuspenseHandler=suspenseHandlerStackCursor.current;suspenseHandlerStackCursor.current=handler}var suspenseHandlerStackCursor={current:null},didWarnAboutNoopUpdateForComponent,didWarnAboutBadClass,didWarnAboutGetDerivedStateOnFunctionalComponent,didWarnAboutStatelessRefs;var fakeInternalInstance={},isArray$1=Array.isArray,didWarnAboutStateTransition;
Object.freeze(fakeInternalInstance);function warnAboutUpdateOnUnmountedLog(fiber,isUnmounted){if(isUnmounted)console.error("Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.");else switch(fiber.tag){case 2:if(0<didWarnAboutNoopUpdateForComponent.size&&didWarnAboutNoopUpdateForComponent.has(fiber.type))break;console.error("Can't perform a React state update on a component that hasn't mounted yet. This is a no-op, but it can indicate a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.");
didWarnAboutNoopUpdateForComponent.add(fiber.type);break;case 1:if(isUnmounted)console.error("Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.");else{isUnmounted=fiber.type;if(0<didWarnAboutNoopUpdateForComponent.size&&didWarnAboutNoopUpdateForComponent.has(isUnmounted))break;console.error("Can't perform a React state update on a component that hasn't mounted yet. This is a no-op, but it can indicate a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.");
didWarnAboutNoopUpdateForComponent.add(isUnmounted)}break;case 0:if(isUnmounted)console.error("Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.");else if(isUnmounted=fiber.type,0<didWarnAboutNoopUpdateForComponent.size&&didWarnAboutNoopUpdateForComponent.has(isUnmounted))break;else console.error("Can't perform a React state update on a component that hasn't mounted yet. This is a no-op, but it can indicate a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function."),
didWarnAboutNoopUpdateForComponent.add(isUnmounted)}}
var offscreenSubtreeIsHidden=null,offscreenSubtreeWasHidden=null;
function checkThenEquqlThen(then,thenThen){return then===thenThen}function bubbleProperties(completedWork){var newChildLanes=NoLanes,subtreeFlags=0;switch(completedWork.tag){case 2:var current=completedWork.alternate;null!==current&&!is$1(current.memoizedState,completedWork.memoizedState)&&markWorkInProgressReceivedUpdate();break;case 1:null!==completedWork.alternate&&!is$1(completedWork.memoizedState,completedWork.memoizedState)&&markWorkInProgressReceivedUpdate();break;case 0:break;case 3:var root=
completedWork.stateNode;root.pendingLanes|=root.suspendedLanes;root.pingedLanes|=root.suspendedLanes;root.suspendedLanes=NoLanes;root.pingedLanes=NoLanes;break;case 5:var wasHydrated=completedWork.stateNode;wasHydrated.current=completedWork;break;case 4:wasHydrated=completedWork.stateNode.containerInfo;break;case 10:wasHydrated=completedWork.type._context;current=completedWork.memoizedProps.value;var oldValue=wasHydrated._currentValue;wasHydrated._currentValue=current;break;case 13:wasHydrated=
completedWork.memoizedState;if(null!==wasHydrated){if(null!==wasHydrated.dehydrated)return completedWork.flags|=32,null;var primaryChildFragment=completedWork.child;primaryChildFragment.flags|=wasHydrated.flags;0!==(completedWork.mode&16)&&(primaryChildFragment.flags|=64);var fallbackFragment=primaryChildFragment.sibling;null!==fallbackFragment?(fallbackFragment.flags|=4,completedWork.flags|=64):primaryChildFragment.flags&=-16777217;null!==wasHydrated.didCapture&&(completedWork.flags|=
128)}break;case 19:wasHydrated=completedWork.memoizedState;if(null!==wasHydrated){wasHydrated=wasHydrated.rendering;if(null!==wasHydrated)for(var i=wasHydrated.child;null!==i;){bubbleProperties(i);i=i.sibling}}break;case 22:wasHydrated=completedWork.memoizedState;wasHydrated="hidden"!==wasHydrated.mode;if(null!==completedWork.alternate&&!wasHydrated&&"hidden"!==completedWork.alternate.memoizedState.mode)for(i=completedWork;null!==i;){var child=i.child;if(null!==child)child.return=i,
i=child;else for(child=i;null!==child;){i=child.sibling;var returnFiber=child.return;child.flags|=8;if(child!==returnFiber.child||null===i){for(;null!==returnFiber;){if(null!==returnFiber.sibling){returnFiber.sibling.return=returnFiber.return;returnFiber=returnFiber.sibling;break}returnFiber=returnFiber.return}child=null}else{i.return=returnFiber;child=i;break}i=child}}}var deletedNodes=null,hydrationParentFiber=null,nextHydratableInstance=null,hydrationCorrectnessWarning=
null;function popToCommonParent(fiberA,fiberB){var parentA=fiberA.return,parentB=fiberB.return;if(null===parentA)return null;if(null===parentB)return null;for(;parentA!==parentB;){var parentAWasHydrated=parentA.alternate;if(null!==parentAWasHydrated&&parentAWasHydrated===parentB)return parentA;var parentBWasHydrated=parentB.alternate;if(null!==parentBWasHydrated&&parentBWasHydrated===parentA)return parentB;parentA=parentA.return;parentB=parentB.return}return parentA}
function findFirstHydrationDOMRef(fiber){var ref=null;fiber=fiber.stateNode;null!==fiber&&(ref=fiber);return ref}function isHydrating(){return null!==hydrationParentFiber}
function getNextHydratableInstance(){var instance=nextHydratableInstance;if(!instance)return null;for(var node=instance;null!==node;node=node.nextSibling)if(1===node.nodeType&&"STYLE"!==node.nodeName&&"SCRIPT"!==node.nodeName){instance=node;break}else instance=void 0;return instance}
function tryHydrate(fiber,nextInstance){switch(fiber.tag){case 5:return fiber.stateNode=nextInstance,hydrationParentFiber=fiber,nextHydratableInstance=getFirstHydratableChild(nextInstance),!0;case 6:return fiber.stateNode=nextInstance,hydrationParentFiber=fiber,nextHydratableInstance=null,!0;case 13:return hydrationParentFiber=fiber,nextHydratableInstance=getFirstHydratableChildWithinSuspenseInstance(nextInstance),!0;case 19:return hydrationParentFiber=fiber,nextHydratableInstance=
getFirstHydratableChild(nextInstance),!0;default:return!1}}
function tryToClaimNextHydratableInstance(fiber){if(isHydrating()){var nextInstance=nextHydratableInstance;if(nextInstance){var firstAttemptedInstance=nextInstance;if(!tryHydrate(fiber,nextInstance)){warnNonSkippableHydrationError(createHydrationError("Error trying to hydrate a React node."));nextInstance=getNextHydratableSibling(firstAttemptedInstance);if(!nextInstance||!tryHydrate(fiber,nextInstance))return fiber.flags|=2,isHydrating=!1,hydrationParentFiber=fiber,
!1}}else return fiber.flags|=2,isHydrating=!1,hydrationParentFiber=fiber,!1}return!0}function warnNonSkippableHydrationError(error){hydrationCorrectnessWarning=error}
function createHydrationError(message){return new Error(message)}function didSuspendOrErrorWhileHydrating(root){return!0}function upgradeHydrationErrorsToRecoverable(){if(null!==hydrationCorrectnessWarning)throw hydrationCorrectnessWarning;}
var suspenseStack=[{type:null,didTimeout:!1,children:null,childLanes:0,pendingLanes:0}];
function getSuspenseHandler(){return suspenseStack[suspenseStack.length-1]}
function findFirstSuspended(row){for(var node=row;null!==node;){if(13===node.tag){var state=node.memoizedState;if(null!==state&&null===state.dehydrated)return node}else if(19===node.tag&&void 0!==node.memoizedProps.revealOrder){if(0!==(node.flags&128))return node}else if(null!==node.child)return node.child.return=node,node=node.child,node;for(;null!==node;){if(node===row)return null;var sibling=node.sibling;if(null!==sibling)return sibling.return=node.return,node=sibling;
node=node.return}node=null}return null}var workInProgressRoot=null,workInProgress=null,renderLanes=NoLanes,workInProgressRootRenderLanes=NoLanes;
function prepareFreshStack(root,lanes){root.finishedWork=null;root.finishedLanes=NoLanes;var timeoutHandle=root.timeoutHandle;-1!==timeoutHandle&&(root.timeoutHandle=-1,cancelTimeout(timeoutHandle));if(null!==workInProgress){for(var interruptedWork=workInProgress.return;null!==interruptedWork;){unwindInterruptedWork(interruptedWork,workInProgress,lanes);interruptedWork=interruptedWork.return}}workInProgressRoot=root;workInProgress=createWorkInProgress(root.current,
null);renderLanes=workInProgressRootRenderLanes=lanes;workInProgress.lanes=lanes;lanes=root.eventTimes;for(var i=0;i<TotalLanes;i++)lanes[i]=-1;lanes=root.expirationTimes;for(i=0;i<TotalLanes;i++)lanes[i]=-1}
function unwindInterruptedWork(interruptedWork,current,renderLanes){var current$alternate=current.alternate;null!==current$alternate&&null!==workInProgress&&isSubsetOfLanes(current$alternate.childLanes,renderLanes)&&(workInProgress.flags|=2);unwindWork(interruptedWork,renderLanes);interruptedWork=interruptedWork.child;for(null!==interruptedWork&&(interruptedWork.return=interruptedWork);null!==interruptedWork;){unwindInterruptedWork(interruptedWork,current,renderLanes);
interruptedWork=interruptedWork.sibling}}
function handleThrow(root,thrownValue){var isWakeable=null!==thrownValue&&"object"==typeof thrownValue&&"function"==typeof thrownValue.then;if("object"==typeof thrownValue&&null!==thrownValue)switch(thrownValue.$$typeof){case REACT_ELEMENT_TYPE:console.error("Objects are not valid as a React child (found: "+("[object Object]"===Object.prototype.toString.call(thrownValue)?"object with keys {"+Object.keys(thrownValue).join(", ")+"}":thrownValue)+"). If you meant to render a collection of children, use an array instead.");
break;case REACT_PORTAL_TYPE:console.error("Portals are not valid as a React child. This likely means that you rendered a Portal, but it wasn't returned from a component's render method. Instead, try moving it to the top level.");break;case REACT_LAZY_TYPE:isWakeable=thrownValue._init;isWakeable=isWakeable(thrownValue._payload);handleThrow(root,isWakeable);return}if(isWakeable)return isWakeable=thrownValue,thrownValue=getSuspenseHandler(),0===(workInProgress.mode&16)?(thrownValue.didTimeout=
!0,renderLanes=NoLanes):(workInProgress.flags|=134217728,workInProgress.lanes=TransitionLane,workInProgress.childLanes=TransitionLane,isWakeable=attachPingListener(root,isWakeable,TransitionLane),void(workInProgressRoot.pingedLanes|=TransitionLane)),void(workInProgressRoot.suspendedLanes|=TransitionLane);thrownValue=createCapturedValue(thrownValue,workInProgress);var erroredWork=workInProgress;do{switch(erroredWork.tag){case 3:erroredWork.flags|=65536;var lane=renderLanes;
thrownValue=createRootErrorUpdate(erroredWork,thrownValue,lane);enqueueCapturedUpdate(erroredWork,thrownValue);return;case 1:if(isWakeable=thrownValue,lane=erroredWork.type,erroredWork=erroredWork.stateNode,0===(erroredWork.flags&6)&&("function"==typeof lane.getDerivedStateFromError||null!==erroredWork&&"function"==typeof erroredWork.componentDidCatch&&!isAlreadyFailedLegacyErrorBoundary(erroredWork))){erroredWork.flags|=65536;var lane$1=renderLanes;isWakeable=createClassErrorUpdate(erroredWork,
isWakeable,lane$1);enqueueCapturedUpdate(erroredWork,isWakeable);return}}erroredWork=erroredWork.return}while(null!==erroredWork)}
function workLoopSync(){for(;null!==workInProgress;)performUnitOfWork(workInProgress)}function workLoopConcurrent(){for(;null!==workInProgress&&!shouldYield();)performUnitOfWork(workInProgress)}
function performUnitOfWork(unitOfWork){var current=unitOfWork.alternate;if(0!==(unitOfWork.mode&32)){var next=beginWork$1(current,unitOfWork,renderLanes);1!==unitOfWork.tag&&2!==unitOfWork.tag&&(current=next)}else next=beginWork$1(current,unitOfWork,renderLanes);if(null===next)completeUnitOfWork(unitOfWork);else workInProgress=next;ReactCurrentOwner.current=null}
function completeUnitOfWork(unitOfWork){var completedWork=unitOfWork;do{var current=completedWork.alternate,returnFiber=completedWork.return;if(0===(completedWork.flags&32768)){var next=completeWork(current,completedWork,renderLanes);if(null!==next){workInProgress=next;return}returnFiber.subtreeFlags|=completedWork.subtreeFlags;returnFiber.childLanes|=completedWork.childLanes}else{next=unwindWork(completedWork,renderLanes);if(null!==next)return next.flags&=-32769,workInProgress=
next,void 0;if(null!==returnFiber)returnFiber.flags|=32768,returnFiber.subtreeFlags=0,returnFiber.deletions=null;workInProgressRoot.flags|=32768;workInProgress=null;return}if(null!==(unitOfWork=completedWork.sibling))return workInProgress=unitOfWork,void 0;workInProgress=completedWork=returnFiber}while(null!==completedWork)}
function commitRoot(root){var renderPriorityLevel=getCurrentPriorityLevel();runWithPriority(ImmediatePriority,commitRootImpl.bind(null,root,renderPriorityLevel));return null}
function commitRootImpl(root,renderPriorityLevel){do try{commitRootImpl$1(root,renderPriorityLevel)}catch(e){if(null===root)throw Error("Should have a root. This is likely a bug in React. Please file an issue.");handleThrow(root,e);continue}while(1,0);return null}
function commitRootImpl$1(root,renderPriorityLevel){var finishedWork=root.finishedWork,lanes=root.finishedLanes;if(null!==finishedWork){root.finishedWork=null;root.finishedLanes=NoLanes;if(finishedWork===root.current)throw Error("Cannot commit the same tree as before. This is likely a bug in React. Please file an issue.");root.callbackNode=null;root.callbackPriority=0;var remainingLanes=finishedWork.lanes|finishedWork.childLanes;markRootFinished(root,remainingLanes);workInProgressRoot=
null;workInProgress=null;renderLanes=NoLanes;legacyErrorBoundaries=null;if(1073741823===lanes)finishedWork=null;else{var firstPendingTime=root.eventTimes[clz32(lanes)-1];1E4<now()-firstPendingTime&&(finishedWork=null)}if(null!==finishedWork){var rootDoesHavePassiveEffects=!1,rootWithPendingPassiveEffects=null;if(0!==root.tag)rootDoesHavePassiveEffects=!0,commitMutationEffects(root,finishedWork);else if(null!==finishedWork.child)finishedWork.child.return=finishedWork,
commitMutationEffects(root,finishedWork);rootDoesHavePassiveEffects?console.log("commitRootImpl: "+rootDoesHavePassiveEffects):console.log("commitRootImpl: "+rootDoesHavePassiveEffects);rootWithPendingPassiveEffects=pendingPassiveEffectsRenderPriority;pendingPassiveEffectsRenderPriority=renderPriorityLevel<NormalPriority?NormalPriority:renderPriorityLevel;pendingPassiveEffectsLanes=lanes;pendingPassiveEffectsRoot=root;null!==legacyErrorBoundaries&&0<legacyErrorBoundaries.size&&
(legacyErrorBoundaries.forEach(function(boundary){console.warn(boundary)}),legacyErrorBoundaries.clear());if(rootDoesHavePassiveEffects){root=root.current;for(remainingLanes=root.alternate;null!==root;){renderPriorityLevel=root;if(0!==(renderPriorityLevel.flags&1024))try{commitHookEffectListUnmount(1032,renderPriorityLevel,renderPriorityLevel.return)}catch(error){captureCommitPhaseError(renderPriorityLevel,renderPriorityLevel.return,error)}if(renderPriorityLevel===remainingLanes)root=
null;else{var sibling=renderPriorityLevel.sibling;if(null!==sibling){sibling.return=renderPriorityLevel.return;root=sibling}else root=renderPriorityLevel.return}}}root=finishedWork;do{renderPriorityLevel=root;var sibling=renderPriorityLevel.alternate,returnFiber=renderPriorityLevel.return;if(0===(renderPriorityLevel.flags&2048)&&null!==sibling)try{commitHookEffectListUnmount(1028,sibling,returnFiber)}catch(error){captureCommitPhaseError(renderPriorityLevel,returnFiber,
error)}root=renderPriorityLevel.sibling}while(null!==root)}var subtreeFlags=finishedWork.subtreeFlags;if(0!==(subtreeFlags&1028)){root=finishedWork;do{if(0!==(root.flags&1024)){subtreeFlags=root.alternate;var current=root;try{var create=current.memoizedState.create;current.memoizedState.destroy=create()}catch(error){captureCommitPhaseError(root,root.return,error)}}root=root.sibling}while(null!==root)}var nearestMountedAncestor=getNearestMountedFiber(finishedWork);root=
finishedWork;do{subtreeFlags=root;current=subtreeFlags.child;for(subtreeFlags.flags&=-2049;null!==current;){var primaryChild=current;var nextParent=primaryChild.return;safelyDetachRef(primaryChild);var next=primaryChild.sibling;if(null!==next){next.return=nextParent;current=next}else current=nextParent}root=root.sibling}while(null!==root);if(1073741823===lanes)console.log("commitRootImpl: "+lanes);else if(root=root.current,null!==root){root=root.alternate;for(finishedWork=
root;null!==finishedWork;){remainingLanes=finishedWork;if(0!==(remainingLanes.flags&1024))try{commitHookEffectListMount(1032,remainingLanes)}catch(error){captureCommitPhaseError(remainingLanes,remainingLanes.return,error)}var sibling=remainingLanes.sibling;if(null!==sibling){sibling.return=remainingLanes.return;finishedWork=sibling}else finishedWork=remainingLanes.return}}subtreeFlags=pendingPassiveEffectsLanes;lanes=pendingPassiveEffectsRenderPriority;finishedWork=pendingPassiveEffectsRoot;
if(null!==finishedWork&&0!==subtreeFlags)if(0!==(subtreeFlags&IdleLane)){var expirationTime=computeExpirationTime(getHighestPriorityLane(subtreeFlags),0);root=finishedWork;var lanes$1=subtreeFlags;root.finishedLanes|=lanes$1;root.finishedWork=null;flushPassiveEffectsImpl(root,expirationTime)}else flushPassiveEffectsImpl(finishedWork,0);ensureRootIsScheduled(root);if(1073741823===root.tag&&null!==root.memoizedState.hydrationCallbacks){root=root.memoizedState.hydrationCallbacks;
for(lanes=0;lanes<root.length;lanes++)finishedWork=root[lanes],finishedWork(null)}root.current=finishedWork;logCommitStopped();IsSomeRendererActing.current=!1;remainingLanes=root;renderPriorityLevel=finishedWork.stateNode;renderPriorityLevel.current=finishedWork;var renderWasConcurrent=!1;lanes=remainingLanes.pendingLanes;0===lanes?legacyErrorBoundaries=null:(renderWasConcurrent=0!==(lanes&4194176),lanes=remainingLanes.suspendedLanes,lanes=remainingLanes.pingedLanes,
lanes=remainingLanes.expiredLanes,lanes=remainingLanes.mutableReadLanes,lanes=remainingLanes.finishedLanes,lanes=renderWasConcurrent?0:lanes,lanes=remainingLanes.pendingLanes,finishedWork=remainingLanes.eventTimes);if(lanes=legacyErrorBoundaries,null!==lanes){legacyErrorBoundaries=null;ensureRootIsScheduled(remainingLanes)}if(renderWasConcurrent&&null!==root)flushSyncCallbacks();return}}throw Error("This is a bug in React. Please file an issue.");}
function captureCommitPhaseError(sourceFiber,returnFiber,error){var errorInfo=createCapturedValue(error,sourceFiber),update=createClassErrorUpdate(sourceFiber,errorInfo,SyncLane);enqueueCapturedUpdate(sourceFiber,update);var isAlreadyFailed=null!==legacyErrorBoundaries;isAlreadyFailed||(legacyErrorBoundaries=new Set);legacyErrorBoundaries.add(sourceFiber)}
function getNearestMountedFiber(fiber){var node=fiber;a:for(;;){for(;null===node.sibling;){if(null===node.return||5===node.return.tag)return node;node=node.return}node.sibling.return=node.return;for(node=node.sibling;5!==node.tag&&6!==node.tag&&18!==node.tag;){if(node.flags&2)continue;if(null===node.child)continue a;node.child.return=node;node=node.child}if(!(node.flags&2))return node.stateNode}return node.stateNode}
function commitMutationEffects(root,finishedWork){root.pendingLanes|=root.suspendedLanes;root.pingedLanes|=root.suspendedLanes;root.suspendedLanes=NoLanes;root.pingedLanes=NoLanes}function commitHookEffectListUnmount(flags,finishedWork,nearestMountedAncestor){var lastEffect=finishedWork.updateQueue;lastEffect=null!==lastEffect?lastEffect.lastEffect:null;if(null!==lastEffect){var firstEffect=lastEffect.next;do{if((firstEffect.tag&flags)===flags){var destroy=firstEffect.destroy;
firstEffect.destroy=void 0;void 0!==destroy&&safelyCallDestroy(finishedWork,nearestMountedAncestor,destroy)}firstEffect=firstEffect.next}while(firstEffect!==lastEffect.next)}}
function commitHookEffectListMount(flags,finishedWork){var lastEffect=finishedWork.updateQueue;lastEffect=null!==lastEffect?lastEffect.lastEffect:null;if(null!==lastEffect){var firstEffect=lastEffect.next;do{if((firstEffect.tag&flags)===flags){var create=firstEffect.create;firstEffect.destroy=create()}firstEffect=firstEffect.next}while(firstEffect!==lastEffect.next)}}
function safelyCallDestroy(current,nearestMountedAncestor,destroy){try{destroy()}catch(error){captureCommitPhaseError(current,nearestMountedAncestor,error)}}function safelyDetachRef(current){var ref=current.ref;if(null!==ref)if("function"==typeof ref)try{ref(null)}catch(error){captureCommitPhaseError(current,current.return,error)}else ref.current=null}function flushPassiveEffects(){if(null!==pendingPassiveEffectsRoot){var root=pendingPassiveEffectsRoot,renderPriority=
pendingPassiveEffectsRenderPriority,lanes=pendingPassiveEffectsLanes;pendingPassiveEffectsRoot=null;pendingPassiveEffectsRenderPriority=NormalPriority;pendingPassiveEffectsLanes=NoLanes;if(0!==(lanes&IdleLane))return;if(renderPriority>LowPriority)return;var expirationTime=computeExpirationTime(getHighestPriorityLane(lanes),0);flushPassiveEffectsImpl(root,expirationTime)}return!1}
function flushPassiveEffectsImpl(root,expirationTime){var prevExecutionContext=executionContext;executionContext|=CommitContext;try{return flushPassiveEffectsImpl$1(root)}finally{executionContext=prevExecutionContext}}
function flushPassiveEffectsImpl$1(root){var lanes=root.pendingLanes;if(0!==lanes)return!1;var finishedWork=root.finishedWork;root.finishedLanes|=lanes;root.finishedWork=null;var rootDoesHavePassiveEffects=!1,rootWithPendingPassiveEffects=null;if(0!==root.tag)rootDoesHavePassiveEffects=!0;else if(null!==finishedWork.child)finishedWork.child.return=finishedWork;rootDoesHavePassiveEffects?console.log("flushPassiveEffectsImpl: "+rootDoesHavePassiveEffects):console.log("flushPassiveEffectsImpl: "+
rootDoesHavePassiveEffects);if(rootDoesHavePassiveEffects){rootDoesHavePassiveEffects=!1;rootWithPendingPassiveEffects=root;var current=root.current;var alternate=current.alternate;commitPassiveEffectDurations(current,root);for(current=alternate;null!==current;){var next=current;if(0!==(next.flags&2048)){var next$jscomp$0=next.updateQueue;next$jscomp$0=null!==next$jscomp$0?next$jscomp$0.lastEffect:null;if(null!==next$jscomp$0){var first=next$jscomp$0.next;do{var tag=first.tag;
if(0!==(tag&4)&&0!==(tag&1)){var current$jscomp$0=next;var nearestMountedAncestor=current$jscomp$0.return;commitHookEffectListUnmount(1028,current$jscomp$0,nearestMountedAncestor);commitHookEffectListMount(1028,current$jscomp$0)}first=first.next}while(first!==next$jscomp$0.next)}}current=current.sibling}for(current=alternate;null!==current;){next=current;if(0!==(next.flags&1024))commitHookEffectListMount(1032,next);current=current.sibling}var devToolsHook=root.current.memoizedState;
if(null!=devToolsHook&&"function"==typeof devToolsHook.onPostCommit)try{devToolsHook.onPostCommit(rendererID)}catch(err){}return!0}return!1}
var ReactCurrentActQueue=Internals.ReactCurrentActQueue;function isLegacyActEnvironment(fiber){return!!(fiber.mode&4)}functionbatchedUpdates(fn,a){var prevExecutionContext=executionContext;executionContext|=BatchedContext;try{return fn(a)}finally{executionContext=prevExecutionContext,0===executionContext&&(flushSyncCallbacks(),scheduleCallback(NormalPriority,flushSyncCallbacks))}}functiondiscreteUpdates(fn,a,b,c){var previousPriority=getCurrentPriorityLevel();
try{return runWithPriority(UserBlockingPriority,fn.bind(null,a,b,c))}finally{}}
function flushSync(fn){if(0!==(executionContext&(RenderContext|CommitContext))){var isSync=0!==(executionContext&RenderContext);isSync?console.error("flushSync was called from inside a lifecycle method. It cannot be called when React is already rendering."):console.error("flushSync was called from inside a lifecycle method. React cannot flush when it is already rendering. Consider moving this call to a scheduler task or micro task.");return}var prevExecutionContext=
executionContext;executionContext|=BatchedContext;var previousPriority=getCurrentPriorityLevel();try{if(fn)return runWithPriority(ImmediatePriority,fn);flushSyncCallbacks()}finally{executionContext=prevExecutionContext,flushSyncCallbacks()}}
function flushSyncCallbacks(){null!==syncQueue&&runWithPriority(ImmediatePriority,flushSyncCallbacks$1)}function flushSyncCallbacks$1(){for(;null!==syncQueue;){var callback=syncQueue;syncQueue=null;callback()}return null}
function scheduleUpdateOnFiber(root,fiber,lane,eventTime){markRootUpdated(root,lane);if(0!==(executionContext&RenderContext)&&root===workInProgressRoot){warnAboutUpdateOnNotYetMountedFiber(fiber);if(0===(executionContext&RenderContext)&&!isLegacyActEnvironment(fiber))if(workInProgressRootRenderLanes===lane||(workInProgress.flags|=2),null===workInProgress)if(fiber=workInProgress=createWorkInProgress(workInProgressRoot.current,null),0!==(fiber.mode&2))for(var i=
root.tag;1>=i;i--)fiber=fiber.child}else{warnAboutRenderPhaseUpdatesInDEV(fiber);if(didScheduleRenderPhaseUpdate)return;var alternate=fiber.alternate;null!==alternate&&null!==currentlyRenderingFiber&&isSubsetOfLanes(alternate.childLanes,renderLanes)?workInProgress=currentlyRenderingFiber:didScheduleRenderPhaseUpdate=workInProgress===currentlyRenderingFiber||null!==alternate&&alternate===currentlyRenderingFiber;workInProgress=currentlyRenderingFiber}else{warnAboutUpdateOnNotYetMountedFiber(fiber);
if(0!==(executionContext&RenderContext))return;fiber=root.current;if(root.isDehydrated&&0===(lane&DefaultHydrationLane))didSuspendOrErrorWhileHydrating(root)&&(root.pendingLanes|=lane);else if(root=root.callbackNode,null!==root&&cancelCallback(root),root=ensureRootIsScheduled(root),null!==root){fiber=root.current.memoizedState;fiber=fiber.isDehydrated;var isRunningHydration=null!==fiber;if(isRunningHydration){var suspendedLanes=getSuspendedLanes(root,renderLanes);markRootSuspended(root,
suspendedLanes)}}}}
function scheduleInitialHydrationOnRoot(root,lane){var current=root.current;root.eventTimes[clz32(lane)-1]=now();current.lanes|=lane;current=root.callbackNode;null!==current&&cancelCallback(current);ensureRootIsScheduled(root)}function isRootScheduled(root){return null!==root.callbackNode}
function ensureRootIsScheduled(root){var lanes=getNextLanes(root,NoLanes);if(0===lanes)return null;var newCallbackPriority=getHighestPriorityLanes(lanes);var existingCallbackPriority=root.callbackPriority;if(newCallbackPriority===existingCallbackPriority&&!(ReactCurrentActQueue.current&&0!==newCallbackPriority))return root.callbackNode;var newCallbackNode;a:switch(laneToRendererPriority(lanes)){case ImmediatePriority:newCallbackPriority=ImmediatePriority;break a;case UserBlockingPriority:newCallbackPriority=
UserBlockingPriority;break a;case NormalPriority:newCallbackPriority=NormalPriority;break a;case LowPriority:newCallbackPriority=LowPriority;break a;case IdlePriority:newCallbackPriority=IdlePriority;break a;default:newCallbackPriority=NormalPriority}existingCallbackPriority=root.current.memoizedState;null!==existingCallbackPriority&&null!==existingCallbackPriority.isDehydrated&&(root.pendingLanes|=DefaultHydrationLane,root.suspendedLanes|=DefaultHydrationLane,newCallbackPriority=
UserBlockingPriority);newCallbackNode=scheduleCallback(newCallbackPriority,performConcurrentWorkOnRoot.bind(null,root));root.callbackPriority=newCallbackPriority;return root.callbackNode=newCallbackNode}
function performConcurrentWorkOnRoot(root,didTimeout){var originalCallbackNode=root.callbackNode;if(didTimeout)return null;var lanes=getNextLanes(root,NoLanes);if(0===lanes)return null;var prevExecutionContext=executionContext;executionContext|=RenderContext;if(root!==workInProgressRoot||lanes!==renderLanes)prepareFreshStack(root,lanes);if(null!==workInProgress){var exitStatus=workLoopConcurrent();if(exitStatus!==0)if(root.finishedWork=root.current.alternate,root.finishedLanes=
lanes,exitStatus===1){if(null===root.tag){exitStatus=workInProgressRoot;var lastSuspendedTime=root.lastSuspendedTime;root.lastSuspendedTime=now();if(0<lastSuspendedTime&&5e3>lastSuspendedTime-root.lastSuspendedTime)root.timeoutHandle=scheduleTimeout(commitRoot.bind(null,root),5e3)}}else if(exitStatus===2)commitRoot(root);else{var thrownValue=workInProgressRoot.thrownValue;workInProgressRoot.thrownValue=null;handleThrow(root,thrownValue)}}if(root.callbackNode===originalCallbackNode)return performConcurrentWorkOnRoot.bind(null,
root)}return null}
function requestUpdateLane(fiber){return isLegacyActEnvironment(fiber)?SyncLane:0!==(executionContext&RenderContext)&&null!==workInProgress?pickArbitraryLane(workInProgress.lanes):TransitionLane}function requestEventTime(){return now()}var scheduleTimeout=setTimeout,cancelTimeout=clearTimeout;function ceiling(num,divisor){return(num/divisor|0)*divisor}
function createMutableSource(owner,getSnapshot){return{_getVersion:function(mostRecentEventTime){return getSnapshot()},_workInProgressVersionPrimary:null,_workInProgressVersionSecondary:null,_source:owner,next:null}}var rendererSigil="r",nextReactIdCounter=0;
function createFiberRoot(containerInfo,tag,hydrate,initialChildren,hydrationCallbacks,isStrictMode,concurrentUpdatesByDefaultOverride,identifierPrefix,onRecoverableError){tag=createHostRootFiber(tag,isStrictMode,concurrentUpdatesByDefaultOverride,identifierPrefix,onRecoverableError);tag.stateNode.containerInfo=containerInfo;tag.stateNode.pendingChildren=initialChildren;tag.stateNode.hydrate=hydrate;tag.stateNode.hydrationCallbacks=hydrationCallbacks;return tag}
function createPortal(children,containerInfo,implementation){var key=implementation;key=null!=key?key.key:null;return{$$typeof:REACT_PORTAL_TYPE,key:key,children,containerInfo,implementation}}
function getPublicRootInstance(container){var containerFiber=container.current;if(!containerFiber.child)return null;switch(containerFiber.child.tag){case 5:return containerFiber.child.stateNode;default:return containerFiber.child.stateNode}}varReactRT={"findDOMNode":null,"hydrate":null,"render":null,"unstable_renderSubtreeIntoContainer":null,"version":"18.2.0"};
function createRoot(container,options){if(!isValidContainer(container))throw Error("createRoot(...): Target container is not a DOM element.");var isStrictMode=!1,concurrentUpdatesByDefaultOverride=!1,identifierPrefix="",onRecoverableError=noop;null!=options&&(null!=options.unstable_strictMode&&"boolean"==typeof options.unstable_strictMode&&(isStrictMode=options.unstable_strictMode),null!=options.unstable_concurrentUpdatesByDefault&&"boolean"==typeof options.unstable_concurrentUpdatesByDefault&&
(concurrentUpdatesByDefaultOverride=options.unstable_concurrentUpdatesByDefault),null!=options.identifierPrefix&&"string"==typeof options.identifierPrefix&&(identifierPrefix=options.identifierPrefix),null!=options.onRecoverableError&&(onRecoverableError=options.onRecoverableError));var root=createFiberRoot(container,1,!1,null,null,isStrictMode,concurrentUpdatesByDefaultOverride,identifierPrefix,onRecoverableError);container[internalContainerInstanceKey]=root.current;listenToAllSupportedEvents(container);
return new ReactDOMRoot(root)}
function ReactDOMRoot(internalRoot){this._internalRoot=internalRoot}
ReactDOMRoot.prototype.render=function(children){var root=this._internalRoot;if(null===root)throw Error("Cannot update an unmounted root.");var container=root.containerInfo;if(0===(executionContext&(RenderContext|CommitContext))){var isFatal=0!==root.tag;if(isFatal)var isMounted=getClosestInstanceFromNode(container);else isMounted=null;var prevExecutionContext=executionContext;executionContext|=RenderContext;isFatal=root.current;if(isFatal.alternate)batchedUpdates(null,
updateContainer.bind(null,root,children,null));else if(isMounted===isFatal)updateContainer(root,children,null);else{var suspendedLanes=getSuspendedLanes(root,NoLanes);markRootSuspended(root,suspendedLanes)}}else updateContainer(root,children,null)};
ReactDOMRoot.prototype.unmount=function(){var root=this._internalRoot;if(null!==root){var container=root.containerInfo;0===(executionContext&(RenderContext|CommitContext))?batchedUpdates(null,unmountComponentAtNode.bind(null,container)):unmountComponentAtNode(container)}};
function createBlockingRoot(container,tag,options){if(!isValidContainer(container))throw Error("createRoot(...): Target container is not a DOM element.");var isStrictMode=!1,concurrentUpdatesByDefaultOverride=!1,identifierPrefix="",onRecoverableError=noop;null!=options&&(null!=options.unstable_strictMode&&"boolean"==typeof options.unstable_strictMode&&(isStrictMode=options.unstable_strictMode),null!=options.unstable_concurrentUpdatesByDefault&&"boolean"==typeof options.unstable_concurrentUpdatesByDefault&&
(concurrentUpdatesByDefaultOverride=options.unstable_concurrentUpdatesByDefault),null!=options.identifierPrefix&&"string"==typeof options.identifierPrefix&&(identifierPrefix=options.identifierPrefix),null!=options.onRecoverableError&&(onRecoverableError=options.onRecoverableError));options=createFiberRoot(container,tag,!1,null,null,isStrictMode,concurrentUpdatesByDefaultOverride,identifierPrefix,onRecoverableError);container[internalContainerInstanceKey]=options.current;
listenToAllSupportedEvents(container);return new ReactDOMBlockingRoot(options)}
function ReactDOMBlockingRoot(internalRoot){this._internalRoot=internalRoot}
ReactDOMBlockingRoot.prototype.render=function(children){var root=this._internalRoot;if(null===root)throw Error("Cannot update an unmounted root.");updateContainer(root,children,null)};ReactDOMBlockingRoot.prototype.unmount=function(){var root=this._internalRoot;if(null!==root){var container=root.containerInfo;updateContainer(root,null,function(){container[internalContainerInstanceKey]=null})}};
function createLegacyRoot(container,options){return new ReactDOMBlockingRoot(createFiberRoot(container,0,null!=options&&!0===options.hydrate,null,null!=options?options.hydrationOptions:null,!1,!1,"",noop))}
function isValidContainer(node){return!!(node&&(1===node.nodeType||9===node.nodeType||11===node.nodeType))}
function findHostInstanceByFiber(fiber){var hostFiber=findCurrentHostFiber(fiber);return null===hostFiber?null:hostFiber.stateNode}
function emptyFindFiberByHostInstance(instance){return null}
function createHydrationWarningManager(){return{onHydratableInstanceMismatch:function(){},onDeletedContainerNodes:function(){},onDeletedHydratableInstance:function(){},onHydratedInstance:function(){}}}
var attemptSynchronousHydration=function(fiber){switch(fiber.tag){case 3:var root=fiber.stateNode;if(isRootDehydrated(root)){var lanes=root.pendingLanes;root.pendingLanes=0;markRootFinished(root,lanes)}break;case 13:flushSync(function(){return scheduleUpdateOnFiber(fiber,fiber,SyncLane)})}},attemptUserBlockingHydration=function(fiber){switch(fiber.tag){case 3:var root=fiber.stateNode;if(isRootDehydrated(root)){var lanes=root.pendingLanes;root.pendingLanes=0;markRootFinished(root,
lanes)}break;case 13:flushSync(function(){return scheduleUpdateOnFiber(fiber,fiber,UserBlockingPriority)})}};function updateContainer(root,children,callback){var current=root.current,eventTime=requestEventTime();var lane=requestUpdateLane(current);var update=createUpdate(lane);update.payload={element:children};null!==callback&&(update.callback=callback);var root$1=enqueueUpdate(current,update,lane);null!==root$1&&scheduleUpdateOnFiber(root$1,current,lane,eventTime)}
function getContextForSubtree(parentComponent){if(!parentComponent)return emptyContextObject;parentComponent=findCurrentUnmaskedContext(parentComponent);return parentComponent.tag===1?parentComponent.stateNode.__reactInternalMemoizedMergedChildContext:emptyContextObject}
function findHostInstanceWithWarning(component,methodName){findCurrentHostFiberWithNoPortals(component);return null}
function unmountComponentAtNode(container){if(!isValidContainer(container))throw Error("unmountComponentAtNode(...): Target container is not a DOM element.");var root=container._reactRootContainer;if(root){if(0!==(executionContext&(RenderContext|CommitContext)))throw Error("unmountComponentAtNode(): Cannot unmount component in the middle of reconciliation. Instead, schedule a callback with useEffect or lay out your component so that unmounting is not necessary during reconciliation.");
root=root._internalRoot;unmountComponentAtNode(container);updateContainer(root,null,function(){root.containerInfo[internalContainerInstanceKey]=null})}container[internalContainerInstanceKey]=null;return!0}
function legacyRenderSubtreeIntoContainer(parentComponent,children,container,forceHydrate,callback){var maybeRoot=container._reactRootContainer;var root;if(maybeRoot)root=maybeRoot._internalRoot,legacyRenderSubtreeIntoContainer(parentComponent,children,container,forceHydrate,callback),updateContainer(root,children,callback);else{maybeRoot=container._reactRootContainer={_internalRoot:null};root=maybeRoot._internalRoot=createLegacyRoot(container,{hydrate:!0,hydrationOptions:null});
if("function"==typeof callback){var originalCallback=callback;callback=function(){var instance=getPublicRootInstance(root);originalCallback.call(instance)}}batchedUpdates(function(){updateContainer(root,children,callback)})}return getPublicRootInstance(root)}
function findDOMNode(componentOrElement){return null==componentOrElement?null:1===componentOrElement.nodeType?componentOrElement:findHostInstanceWithWarning(componentOrElement,"findDOMNode")}
function hydrate(element,container,callback){if(!isValidContainer(container))throw Error("hydrate(...): Target container is not a DOM element.");return legacyRenderSubtreeIntoContainer(null,element,container,!0,callback)}
function render(element,container,callback){if(!isValidContainer(container))throw Error("render(...): Target container is not a DOM element.");return legacyRenderSubtreeIntoContainer(null,element,container,!1,callback)}
function unstable_renderSubtreeIntoContainer(parentComponent,element,containerNode,callback){if(!isValidContainer(containerNode))throw Error("render(...): Target container is not a DOM element.");if(null==parentComponent||void 0===parentComponent._reactInternals)throw Error("parentComponent must be a valid React Component");return legacyRenderSubtreeIntoContainer(parentComponent,element,containerNode,!1,callback)}var ReactVersion="18.2.0";
ReactRT.findDOMNode=findDOMNode;ReactRT.hydrate=hydrate;ReactRT.render=render;ReactRT.unstable_renderSubtreeIntoContainer=unstable_renderSubtreeIntoContainer;var flushControlled=function(fn){var prevExecutionContext=executionContext;executionContext|=BatchedContext;var previousPriority=getCurrentPriorityLevel();try{return runWithPriority(ImmediatePriority,fn)}finally{executionContext=prevExecutionContext,0===executionContext&&(flushSyncCallbacks(),scheduleCallback(NormalPriority,
flushSyncCallbacks))}},scheduleHydration=function(fn){if(0!==(executionContext&RenderContext))throw Error("Cannot hydrate inside a nested render. This is likely a bug in React. Please file an issue.");scheduleCallback(NormalPriority,fn)};
function onRecoverableError(error){console.error(error)}function createPortal$1(children,container){var key=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!isValidContainer(container))throw Error("createPortal(...): Target container is not a DOM element.");return createPortal(children,container,null,key)}
var Internals$1=Internals;function createRoot$1(container,options){return createRoot(container,options)}function hydrateRoot(container,initialChildren,options){return createBlockingRoot(container,1,options)}var attemptContinuousHydration=function(fiber){if(13===fiber.tag){var lane=InputContinuousLane;scheduleUpdateOnFiber(fiber,fiber,lane);scheduleInitialHydrationOnRoot(fiber,lane)}};
var attemptHydrationAtCurrentPriority=function(fiber){if(13===fiber.tag){var lane=requestUpdateLane(fiber);scheduleUpdateOnFiber(fiber,fiber,lane);scheduleInitialHydrationOnRoot(fiber,lane)}};var getCurrentUpdatePriority=requestUpdateLane;var runWithPriority=function(priority,fn){var previousPriority=getCurrentPriorityLevel();try{return runWithPriority(priority,fn)}finally{}};var findFiberByHostInstance=emptyFindFiberByHostInstance,bundleType=0,version$1=ReactVersion,
rendererPackageName="react-dom";var flushedReactCurrentDispatcher;
function getComponentNameFromFiber(fiber){var type=fiber.type;switch(fiber.tag){case 2:if("function"==typeof type)return type.displayName||type.name||"Component";if("object"==typeof type&&null!==type)return type.displayName||"Context.Consumer";break;case 1:return type.displayName||type.name||"Component";case 0:var context=type._context;return context.displayName||"Context.Provider";case 22:return"Offscreen";case 5:return type;case 4:return"Portal";case 11:type=type.render;
return type.displayName||type.name||"ForwardRef";case 13:return"Suspense";case 19:return"SuspenseList";case 10:return"Context.Provider"}}
function findCurrentFiberUsingSlowPath(serverFiber){var devToolsHook=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(void 0===devToolsHook)return null;var fiber=devToolsHook.getFiberFromHostInstance(serverFiber);return findCurrentHostFiber(fiber)}var findCurrentHostFiber=function(parent){var currentParent=findCurrentFiberUsingSlowPath(parent);return null===currentParent?null:currentParent.stateNode};
function findCurrentUnmaskedContext(fiber){if(0!==(fiber.mode&2))return emptyContextObject;for(var node=fiber;null!==node;){if(1===node.tag&&null!==node.stateNode){var instance=node.stateNode;if("function"==typeof instance.getChildContext){var childContext=instance.getChildContext();for(var key in childContext)if(!hasOwnProperty$1.call(childContext,key))throw Error((getComponentNameFromFiber(node)||"Unknown")+'.getChildContext(): key "'+key+'" is not defined in childContext.');
return assign({},node.stateNode.__reactInternalMemoizedMergedChildContext,childContext)}}if(10===node.tag&&node.type._context._currentValue)return node.type._context._currentValue;node=node.return}return emptyContextObject}var findCurrentHostFiberWithNoPortals=function(parent){var currentParent=findCurrentFiberUsingSlowPath(parent);if(null===currentParent)return null;for(var findChildAgreement=currentParent;null!==findChildAgreement;){if(5===findChildAgreement.tag||6===
findChildAgreement.tag)return findChildAgreement;findChildAgreement=findChildAgreement.child}return null};
function resolveOwner(fiber){for(;null!==fiber.return;)fiber=fiber.return;return 3===fiber.tag?fiber.stateNode.containerInfo.ownerDocument.documentElement:null}function getOwnerDocumentFromRoot(root){return root.ownerDocument}var injection={injectFiberControlledHostComponent:function(hostComponentImpl){injection.HostComponent=hostComponentImpl}};
function batchedUpdates$1(fn,a,b){if(0!==(executionContext&RenderContext)){var isSync=0!==(executionContext&RenderContext);isSync?console.error("batchedUpdates was called from inside a lifecycle method. It cannot be called when React is already rendering."):console.error("batchedUpdates was called from inside a lifecycle method. React cannot flush when it is already rendering. Consider moving this call to a scheduler task or micro task.")}return batchedUpdates(fn,a,b)}
function flushSync$1(fn,a){if(0!==(executionContext&RenderContext)){var isSync=0!==(executionContext&RenderContext);isSync?console.error("flushSync was called from inside a lifecycle method. It cannot be called when React is already rendering."):console.error("flushSync was called from inside a lifecycle method. React cannot flush when it is already rendering. Consider moving this call to a scheduler task or micro task.")}return flushSync(fn,a)}
function unstable_createEventHandle(type,options){if(!topLevelEventsToReactNames.has(type))throw Error("Cannot call unstable_createEventHandle for "+('`"'+type+'"`, because it is not a supported event type.'));var doc=resolveOwner(this);var eventHandle=[type,doc];if(null!=options){if(options.capture)return eventHandle.push(options.capture),eventHandle}return eventHandle}var SecretInternals={__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Internals$1},unstable_batchedUpdates=
batchedUpdates$1,unstable_createPortal=createPortal$1,unstable_flushControlled=flushControlled;var unstable_renderSubtreeIntoContainer$1=unstable_renderSubtreeIntoContainer,version=ReactVersion,createPortal$2=createPortal$1;var render$1=render,hydrate$1=hydrate,findDOMNode$1=findDOMNode,unmountComponentAtNode$1=unmountComponentAtNode;var createRoot$2=createRoot$1,hydrateRoot$1=hydrateRoot,unstable_createEventHandle$1=unstable_createEventHandle;
export{SecretInternals as __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,createPortal$2 as createPortal,createRoot$2 as createRoot,findDOMNode$1 as findDOMNode,flushSync$1 as flushSync,hydrate$1 as hydrate,hydrateRoot$1 as hydrateRoot,render$1 as render,unmountComponentAtNode$1 as unmountComponentAtNode,unstable_batchedUpdates,unstable_createEventHandle$1 as unstable_createEventHandle,unstable_createPortal,unstable_flushControlled,unstable_renderSubtreeIntoContainer$1 as unstable_renderSubtreeIntoContainer,version};
