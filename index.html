<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Link Extractor Pro</title>
    <link rel="stylesheet" href="/public/tailwind.min.css">
    
    <script>
        // Set language and text direction for proper internationalization support
        try {
            if (chrome && chrome.i18n) {
                const uiLang = chrome.i18n.getUILanguage();
                document.documentElement.lang = uiLang;
                const locale = chrome.i18n.getMessage('@@ui_locale');
                if (locale) {
                    document.documentElement.dir = locale.startsWith('ar') ? 'rtl' : 'ltr';
                }
            }
        } catch (e) {
            // Fallback for development environments where chrome API is not available
            console.warn("Could not set language and direction from Chrome API. Defaulting to 'en' and 'ltr'.");
            document.documentElement.lang = 'en';
            document.documentElement.dir = 'ltr';
        }
    </script>
<script type="importmap">
{
  "imports": {
    "react": "/public/react.js",
    "react-dom": "/public/react-dom.js",
    "react-dom/client": "/public/react-dom-client.js",
    "scheduler": "/public/scheduler.js",
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "react/": "https://aistudiocdn.com/react@^19.1.1/"
  }
}
</script>
</head>
<body class="bg-gray-900">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>
