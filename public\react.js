/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Dispatcher={current:null},Dispatcher_1=Dispatcher;function App(){}var REACT_ELEMENT_TYPE=Symbol.for("react.element"),REACT_PORTAL_TYPE=Symbol.for("react.portal"),REACT_FRAGMENT_TYPE=Symbol.for("react.fragment"),REACT_STRICT_MODE_TYPE=Symbol.for("react.strict_mode"),REACT_PROFILER_TYPE=Symbol.for("react.profiler"),REACT_PROVIDER_TYPE=Symbol.for("react.provider"),REACT_CONTEXT_TYPE=Symbol.for("react.context"),REACT_FORWARD_REF_TYPE=Symbol.for("react.forward_ref"),REACT_SUSPENSE_TYPE=Symbol.for("react.suspense"),REACT_SUSPENSE_LIST_TYPE=Symbol.for("react.suspense_list"),
REACT_MEMO_TYPE=Symbol.for("react.memo"),REACT_LAZY_TYPE=Symbol.for("react.lazy"),REACT_OFFSCREEN_TYPE=Symbol.for("react.offscreen"),MAYBE_ITERATOR_SYMBOL=Symbol.iterator;function getIteratorFn(maybeIterable){if(null===maybeIterable||"object"!=typeof maybeIterable)return null;maybeIterable=MAYBE_ITERATOR_SYMBOL&&maybeIterable[MAYBE_ITERATOR_SYMBOL]||maybeIterable["@@iterator"];return"function"==typeof maybeIterable?maybeIterable:null}
var ReactCurrentOwner={current:null},ReactCurrentOwner_1=ReactCurrentOwner,ReactDebugCurrentFrame={current:null},ReactDebugCurrentFrame_1=ReactDebugCurrentFrame,hasOwnProperty=Object.prototype.hasOwnProperty,RESERVED_PROPS={key:!0,ref:!0,__self:!0,__source:!0};
function hasValidRef(config){return void 0!==config.ref}function hasValidKey(config){return void 0!==config.key}
function ReactElement(type,key,ref,self,source,owner,props){return{$$typeof:REACT_ELEMENT_TYPE,type,key,ref,props,
_owner:owner}}
ReactElement.createElement=function(type,config,children){var propName,props={},key=null,ref=null;if(null!=config)for(propName in hasValidRef(config)&&(ref=config.ref),hasValidKey(config)&&(key=""+config.key),void 0===config.__self||(self=config.__self),void 0===config.__source||(source=config.__source),config)hasOwnProperty.call(config,propName)&&!RESERVED_PROPS.hasOwnProperty(propName)&&(props[propName]=config[propName]);var childrenLength=arguments.length-2;if(1===childrenLength)props.children=
children;else if(1<childrenLength){for(var childArray=Array(childrenLength),i=0;i<childrenLength;i++)childArray[i]=arguments[i+2];props.children=childArray}if(type&&type.defaultProps)for(propName in childrenLength=type.defaultProps,childrenLength)void 0===props[propName]&&(props[propName]=childrenLength[propName]);return ReactElement(type,key,ref,self,source,ReactCurrentOwner_1.current,props)};
ReactElement.createFactory=function(type){var factory=ReactElement.createElement.bind(null,type);factory.type=type;return factory};ReactElement.cloneAndReplaceKey=function(oldElement,newKey){return ReactElement(oldElement.type,newKey,oldElement.ref,oldElement._self,oldElement._source,oldElement._owner,oldElement.props)};
ReactElement.cloneElement=function(element,config,children){if(null==element)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+element+".");var propName,props=Object.assign({},element.props),key=element.key,ref=element.ref,owner=element._owner;if(null!=config){hasValidRef(config)&&(ref=config.ref,owner=ReactCurrentOwner_1.current);hasValidKey(config)&&(key=""+config.key);if(element.type&&element.type.defaultProps)var defaultProps=element.type.defaultProps;
for(propName in config)hasOwnProperty.call(config,propName)&&!RESERVED_PROPS.hasOwnProperty(propName)&&(props[propName]=void 0===config[propName]&&void 0!==defaultProps?defaultProps[propName]:config[propName])}children=arguments.length-2;if(1===children)props.children=children;else if(1<children){defaultProps=Array(children);for(var i=0;i<children;i++)defaultProps[i]=arguments[i+2];props.children=defaultProps}return ReactElement(element.type,key,ref,self,source,owner,props)};
ReactElement.isValidElement=function(object){return"object"==typeof object&&null!==object&&object.$$typeof===REACT_ELEMENT_TYPE};var SEPARATOR=".",SUBSEPARATOR=":";
function escape(key){var escapeRegex=/[=:]/g,escaperLookup={"=":"=0",":":"=2"};return"$"+key.replace(escapeRegex,function(match){return escaperLookup[match]})}var didWarnAboutMaps=!1,userProvidedKeyEscapeRegex=/\/+/g;function escapeUserProvidedKey(text){return text.replace(userProvidedKeyEscapeRegex,"$/&")}
function getElementKey(element,index){if("object"==typeof element&&null!==element&&null!=element.key)return escape(""+element.key);return index.toString(36)}
function mapIntoArray(children,array,escapedPrefix,nameSoFar,callback){var type=typeof children;if("undefined"===type||"boolean"===type)children=null;var invokeCallback=!1;if(null===children)invokeCallback=!0;else switch(type){case "string":case "number":invokeCallback=!0;break;case "object":switch(children.$$typeof){case REACT_ELEMENT_TYPE:case REACT_PORTAL_TYPE:invokeCallback=!0}}if(invokeCallback)return callback=callback(children),invokeCallback=
""===nameSoFar?SEPARATOR+getElementKey(children,0):nameSoFar,Array.isArray(callback)?(escapedPrefix="",null!=nameSoFar&&(escapedPrefix=nameSoFar+SUBSEPARATOR),callback.forEach(function(child){mapIntoArray(child,array,escapedPrefix,"",function(c){return c})})):null!=callback&&("object"==typeof callback&&null!==callback&&null!=callback.key&&(!didWarnAboutMaps&&callback.key===children.key&&(didWarnAboutMaps=!0,console.warn("Each child in a list should have a unique 'key' prop. See https://reactjs.org/link/warning-keys for more information.")),
key=""+callback.key),ReactElement.isValidElement(callback)?(callback=ReactElement.cloneAndReplaceKey(callback,escapedPrefix+(""+callback.key===children.key?escapeUserProvidedKey(""+callback.key):"")+SEPARATOR+key),array.push(callback)):(key=escapedPrefix+escapeUserProvidedKey(""+callback.key)+SEPARATOR+key,array.push(callback))),1;invokeCallback=0;nameSoFar=""===nameSoFar?SEPARATOR:nameSoFar+SUBSEPARATOR;if(Array.isArray(children))for(var i=0;i<children.length;i++)type=
children[i],invokeCallback+=mapIntoArray(type,array,escapedPrefix,nameSoFar+getElementKey(type,i),callback);else if(i=getIteratorFn(children),null!==i)for(children=i.call(children),i=0;!(type=children.next()).done;)type=type.value,invokeCallback+=mapIntoArray(type,array,escapedPrefix,nameSoFar+getElementKey(type,i++),callback);else if("object"===type)throw children=""+children,Error("Objects are not valid as a React child (found: "+("[object Object]"===children?"object with keys {"+Object.keys(children).join(", ")+
"}":children)+"). If you meant to render a collection of children, use an array instead.");return invokeCallback}
function mapChildren(children,func,context){if(null==children)return children;var result=[],count=0;mapIntoArray(children,result,"","",function(child){return func.call(context,child,count++)});return result}var Children={map:mapChildren,forEach:function(children,func,context){mapChildren(children,function(){func.apply(this,arguments)},context)},count:function(children){var n=0;mapChildren(children,function(){n++});return n},toArray:function(children){return mapChildren(children,function(child){return child})||
[]},only:function(children){if(!ReactElement.isValidElement(children))throw Error("React.Children.only expected to receive a single React element child.");return children}},ReactCurrentActQueue={current:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1};function Component(props,context,updater){this.props=props;this.context=context;this.refs=App;this.updater=updater||{isMounted:function(){return!1},enqueueForceUpdate:function(){return null},enqueueReplaceState:function(){return null},enqueueSetState:function(){return null}}}
Component.prototype.isReactComponent={};Component.prototype.setState=function(partialState,callback){if("object"!=typeof partialState&&"function"!=typeof partialState&&null!=partialState)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,partialState,callback,"setState")};Component.prototype.forceUpdate=function(callback){this.updater.enqueueForceUpdate(this,callback,"forceUpdate")};
function PureComponent(props,context,updater){this.props=props;this.context=context;this.refs=App;this.updater=updater||{isMounted:function(){return!1},enqueueForceUpdate:function(){return null},enqueueReplaceState:function(){return null},enqueueSetState:function(){return null}}}var pureComponentPrototype=Component.prototype;PureComponent.prototype=Object.create(pureComponentPrototype);PureComponent.prototype.constructor=PureComponent;Object.assign(PureComponent.prototype,Component.prototype);
PureComponent.prototype.isPureReactComponent=!0;var ReactCurrentDispatcher=Dispatcher_1,ReactCurrentBatchConfig={transition:null},ReactCurrentBatchConfig_1=ReactCurrentBatchConfig;function getCacheSignal(){return readContext(ReactSharedInternals.ReactCurrentCache).controller.signal}function getCacheForType(resourceType){var dispatcher=ReactCurrentDispatcher.current;if(!dispatcher)return resourceType();var currentCache=readContext(ReactSharedInternals.ReactCurrentCache);return(dispatcher=currentCache.res)?"function"==typeof dispatcher.get?
(resourceType=dispatcher.get(resourceType))?resourceType:void 0:void 0:void 0}var ReactSharedInternals={ReactCurrentDispatcher:Dispatcher_1,ReactCurrentBatchConfig:ReactCurrentBatchConfig_1,ReactCurrentOwner:ReactCurrentOwner_1};function readContext(context){return(ReactCurrentDispatcher.current.readContext||function(context){return context._currentValue})(context)}
var Children_1=Children,Fragment=REACT_FRAGMENT_TYPE,Profiler=REACT_PROFILER_TYPE,StrictMode=REACT_STRICT_MODE_TYPE,Suspense=REACT_SUSPENSE_TYPE,SuspenseList=REACT_SUSPENSE_LIST_TYPE,act=function(callback){var actScopeDepth=0;return function(){var prevIsBatchingLegacy=ReactCurrentActQueue.isBatchingLegacy,prevActScopeDepth=actScopeDepth;actScopeDepth++;ReactCurrentActQueue.isBatchingLegacy=!0;try{var result=callback.apply(void 0,arguments);if("object"==typeof result&&null!==result&&"function"==
typeof result.then)return{then:function(resolve,reject){var originalThen=result.then;originalThen.call(result,function(resolvedValue){actScopeDepth--;if(0===actScopeDepth){var prevIsBatchingLegacy=ReactCurrentActQueue.isBatchingLegacy;ReactCurrentActQueue.isBatchingLegacy=!1;try{resolve(resolvedValue)}finally{ReactCurrentActQueue.isBatchingLegacy=prevIsBatchingLegacy}}else resolve(resolvedValue)},function(rejectedValue){actScopeDepth--;0===actScopeDepth?
(prevIsBatchingLegacy=ReactCurrentActQueue.isBatchingLegacy,ReactCurrentActQueue.isBatchingLegacy=!1,reject(rejectedValue)):reject(rejectedValue)})}}}catch(error){throw actScopeDepth--,0===actScopeDepth&&(ReactCurrentActQueue.isBatchingLegacy=prevIsBatchingLegacy),error}finally{actScopeDepth--,0===actScopeDepth&&(ReactCurrentActQueue.isBatchingLegacy=prevIsBatchingLegacy)}}},
createElement=ReactElement.createElement,cloneElement=ReactElement.cloneElement,createContext=function(defaultValue){defaultValue={$$typeof:REACT_CONTEXT_TYPE,_currentValue:defaultValue,_currentValue2:defaultValue,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};defaultValue.Provider={$$typeof:REACT_PROVIDER_TYPE,_context:defaultValue};return defaultValue.Consumer=defaultValue},createFactory=ReactElement.createFactory,
createRef=function(){return{current:null}},forwardRef=function(render){return{$$typeof:REACT_FORWARD_REF_TYPE,render}},isValidElement=ReactElement.isValidElement,lazy=function(ctor){var lazyType={$$typeof:REACT_LAZY_TYPE,_payload:{_status:-1,_result:ctor},_init:function(payload){if(-1===payload._status){var ctor=payload._result;ctor=ctor();if("object"!=typeof ctor||null===ctor||"function"!=typeof ctor.then)throw Error("lazy: Expected the result of a dynamic import() call. Instead received: "+
ctor);var pending=payload;pending._status=0;pending._result=ctor;ctor.then(function(moduleObject){if(0===payload._status){var ctor=moduleObject.default;if("function"!=typeof ctor&&"object"!=typeof ctor&&null!==ctor)throw Error("lazy: Expected the result of a dynamic import() call. Instead received: "+ctor);var resolved=payload;resolved._status=1;resolved._result=ctor}},function(error){if(0===payload._status){var rejected=payload;rejected._status=2;rejected._result=error}})}}};return lazyType},
memo=function(type,compare){return{$$typeof:REACT_MEMO_TYPE,type,compare:"function"==typeof compare?compare:null}},cache=function(fn){return function(){return getCacheForType(fn).apply(void 0,arguments)}},startTransition=function(scope,options){return(ReactCurrentBatchConfig_1.transition||function(scope,options){var prevTransition=ReactCurrentBatchConfig_1.transition;ReactCurrentBatchConfig_1.transition={};try{scope()}finally{ReactCurrentBatchConfig_1.transition=
prevTransition}}).call(this,scope,options)},unstable_useDeferredValue=function(value,initialValue){return(ReactCurrentDispatcher.current.useDeferredValue||function(value,initialValue){return value})(value,initialValue)},unstable_useTransition=function(){return(ReactCurrentDispatcher.current.useTransition||function(){return[!1,function(scope,options){startTransition(scope,options)}]})()},useCallback=function(callback,deps){return ReactCurrentDispatcher.current.useCallback(callback,
deps)},useContext=function(context){return ReactCurrentDispatcher.current.useContext(context)},useDebugValue=function(value,formatterFn){},useEffect=function(create,deps){return ReactCurrentDispatcher.current.useEffect(create,deps)},useId=function(){return ReactCurrentDispatcher.current.useId()},useImperativeHandle=function(ref,create,deps){return ReactCurrentDispatcher.current.useImperativeHandle(ref,create,deps)},useInsertionEffect=function(create,deps){return ReactCurrentDispatcher.current.useInsertionEffect(create,
deps)},useLayoutEffect=function(create,deps){return ReactCurrentDispatcher.current.useLayoutEffect(create,deps)},useMemo=function(create,deps){return ReactCurrentDispatcher.current.useMemo(create,deps)},useReducer=function(reducer,initialArg,init){return ReactCurrentDispatcher.current.useReducer(reducer,initialArg,init)},useRef=function(initialValue){return ReactCurrentDispatcher.current.useRef(initialValue)},useState=function(initialState){return ReactCurrentDispatcher.current.useState(initialState)},
useSyncExternalStore=function(subscribe,getSnapshot,getServerSnapshot){return ReactCurrentDispatcher.current.useSyncExternalStore(subscribe,getSnapshot,getServerSnapshot)},useTransition=function(){return(ReactCurrentDispatcher.current.useTransition||function(){return[!1,function(scope,options){startTransition(scope,options)}]})()},version="18.2.0";function experimental_useEffectEvent(callback){var ref=useRef(null);useLayoutEffect(function(){ref.current=callback});
var ref$1=useRef(null);null===ref$1.current&&(ref$1.current=function(){return ref.current.apply(void 0,arguments)});return ref$1.current}var React=Object.freeze({__proto__:null,Children:Children_1,Fragment,Profiler,PureComponent,StrictMode,Suspense,SuspenseList,act,cache,cloneElement,Component,createContext,createElement,createFactory,createRef,forwardRef,isValidElement,lazy,memo,startTransition,unstable_useDeferredValue,unstable_useTransition,useCallback,useContext,useDebugValue,useEffect,
useId,useImperativeHandle,useInsertionEffect,useLayoutEffect,useMemo,useReducer,useRef,useState,useSyncExternalStore,useTransition,version,experimental_useEffectEvent,experimental_useOptimistic:function(passthrough,reducer){return useReducer(reducer,passthrough)},use:function(usable){return(ReactCurrentDispatcher.current.use||function(usable){if("object"==typeof usable&&null!==usable)if("function"==typeof usable.then){var thenable=usable;switch(thenable.status){case "fulfilled":return thenable.value;
case "rejected":throw thenable.reason;case "pending":throw thenable;default:usable.status="pending";usable.then(function(fulfilledValue){usable.status="fulfilled";usable.value=fulfilledValue},function(error){usable.status="rejected";usable.reason=error});throw usable}}return usable._currentValue})(usable)}});var React$1=Object.freeze({__proto__:null,default:React});
function get(key){return React[key]}var esm=Object.create(React);esm.default=React$1;esm.get=get;var SecretInternals={__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ReactSharedInternals},Children_2=Children_1,Component_1=Component,Fragment_1=Fragment,Profiler_1=Profiler,PureComponent_1=PureComponent,StrictMode_1=StrictMode,Suspense_1=Suspense,cloneElement_1=cloneElement,createContext_1=createContext,createElement_1=createElement,createFactory_1=createFactory,createRef_1=createRef,
forwardRef_1=forwardRef,isValidElement_1=isValidElement,lazy_1=lazy,memo_1=memo,startTransition_1=startTransition,useCallback_1=useCallback,useContext_1=useContext,useDebugValue_1=useDebugValue,useDeferredValue=unstable_useDeferredValue,useEffect_1=useEffect,useId_1=useId,useImperativeHandle_1=useImperativeHandle,useInsertionEffect_1=useInsertionEffect,useLayoutEffect_1=useLayoutEffect,useMemo_1=useMemo,useReducer_1=useReducer,useRef_1=useRef,useState_1=useState,useSyncExternalStore_1=useSyncExternalStore,
useTransition_1=useTransition,version_1=version,unstable_act=act;export{SecretInternals as __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Children_2 as Children,Component_1 as Component,Fragment_1 as Fragment,Profiler_1 as Profiler,PureComponent_1 as PureComponent,StrictMode_1 as StrictMode,Suspense_1 as Suspense,cloneElement_1 as cloneElement,createContext_1 as createContext,createElement_1 as createElement,createFactory_1 as createFactory,createRef_1 as createRef,forwardRef_1 as forwardRef,isValidElement_1 as isValidElement,lazy_1 as lazy,memo_1 as memo,startTransition_1 as startTransition,unstable_act,useCallback_1 as useCallback,useContext_1 as useContext,useDebugValue_1 as useDebugValue,useDeferredValue,useEffect_1 as useEffect,useId_1 as useId,useImperativeHandle_1 as useImperativeHandle,useInsertionEffect_1 as useInsertionEffect,useLayoutEffect_1 as useLayoutEffect,useMemo_1 as useMemo,useReducer_1 as useReducer,useRef_1 as useRef,useState_1 as useState,useSyncExternalStore_1 as useSyncExternalStore,useTransition_1 as useTransition,version_1 as version};
