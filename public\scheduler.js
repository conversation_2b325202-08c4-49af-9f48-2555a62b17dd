/*
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

'use strict';

function f(a, b) {
  var c = a.length;
  a.push(b);
  a: for (; 0 < c; ) {
    var d = (c - 1) >>> 1,
      e = a[d];
    if (0 < g(e, b)) (a[d] = b), (a[c] = e), (c = d);
    else break a;
  }
}
function h(a) {
  return 0 === a.length ? null : a[0];
}
function k(a) {
  if (0 === a.length) return null;
  var b = a[0],
    c = a.pop();
  if (c !== b) {
    a[0] = c;
    a: for (var d = 0, e = a.length, l = e >>> 1; d < l; ) {
      var m = 2 * (d + 1) - 1,
        p = a[m],
        q = m + 1,
        r = a[q];
      if (0 > g(p, c))
        q < e && 0 > g(r, p)
          ? ((a[d] = r), (a[q] = c), (d = q))
          : ((a[d] = p), (a[m] = c), (d = m));
      else if (q < e && 0 > g(r, c)) (a[d] = r), (a[q] = c), (d = q);
      else break a;
    }
  }
  return b;
}
function g(a, b) {
  var c = a.sortIndex - b.sortIndex;
  return 0 !== c ? c : a.id - b.id;
}
if ("object" === typeof performance && "function" === typeof performance.now) {
  var t = performance;
  var u = t.now.bind(t);
} else {
  var v = Date.now();
  u = function () {
    return Date.now() - v;
  };
}
var w = [],
  x = [],
  y = 1,
  z = null,
  A = 3,
  B = !1,
  C = !1,
  D = !1,
  E = "function" === typeof setTimeout ? setTimeout : null,
  F = "function" === typeof clearTimeout ? clearTimeout : null,
  G = "undefined" !== typeof setImmediate ? setImmediate : null;
"undefined" !== typeof navigator &&
  void 0 !== navigator.scheduling &&
  void 0 !== navigator.scheduling.isInputPending &&
  navigator.scheduling.isInputPending.bind(navigator.scheduling);
function H(a) {
  for (var b = h(x); null !== b; ) {
    if (null === b.callback) k(x);
    else if (b.startTime <= a)
      k(x), (b.sortIndex = b.expirationTime), f(w, b);
    else break;
    b = h(x);
  }
}
function I(a) {
  D = !1;
  H(a);
  if (!C)
    if (null !== h(w)) (C = !0), L(J);
    else {
      var b = h(x);
      null !== b && M(I, b.startTime - a);
    }
}
function J(a, b) {
  C = !1;
  D && ((D = !1), F(N), (N = -1));
  B = !0;
  var c = A;
  try {
    H(b);
    for (
      z = h(w);
      null !== z && (!(z.expirationTime > b) || (a && !O()));

    ) {
      var d = z.callback;
      if ("function" === typeof d) {
        z.callback = null;
        A = z.priorityLevel;
        var e = d(z.expirationTime <= b);
        b = u();
        "function" === typeof e ? (z.callback = e) : z === h(w) && k(w);
        H(b);
      } else k(w);
      z = h(w);
    }
    if (null !== z) var l = !0;
    else {
      var m = h(x);
      null !== m && M(I, m.startTime - b);
      l = !1;
    }
    return l;
  } finally {
    (z = null), (A = c), (B = !1);
  }
}
var K = !1,
  P = null,
  N = -1,
  Q = 5,
  R = -1;
function O() {
  return u() - R < Q ? !1 : !0;
}
function S() {
  if (null !== P) {
    var a = u();
    R = a;
    var b = !0;
    try {
      b = P(!0, a);
    } finally {
      b ? T() : ((K = !1), (P = null));
    }
  } else K = !1;
}
var T;
if ("function" === typeof G)
  T = function () {
    G(S);
  };
else if ("undefined" !== typeof MessageChannel) {
  var U = new MessageChannel(),
    V = U.port2;
  U.port1.onmessage = S;
  T = function () {
    V.postMessage(null);
  };
} else
  T = function () {
    E(S, 0);
  };
function L(a) {
  P = a;
  K || ((K = !0), T());
}
function M(a, b) {
  N = E(function () {
    a(u());
  }, b);
}
var unstable_now = u;
var unstable_IdlePriority = 5;
var unstable_ImmediatePriority = 1;
var unstable_LowPriority = 4;
var unstable_NormalPriority = 3;
var unstable_UserBlockingPriority = 2;
var unstable_cancelCallback = function (a) {
  a.callback = null;
};
var unstable_continueExecution = function () {
  C || B || ((C = !0), L(J));
};
var unstable_forceFrameRate = function (a) {
  0 > a || 125 < a
    ? console.error(
        "forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"
      )
    : (Q = 0 < a ? Math.floor(1e3 / a) : 5);
};
var unstable_getCurrentPriorityLevel = function () {
  return A;
};
var unstable_getFirstCallbackNode = function () {
  return h(w);
};
var unstable_next = function (a) {
  switch (A) {
    case 1:
    case 2:
    case 3:
      var b = 3;
      break;
    default:
      b = A;
  }
  var c = A;
  A = b;
  try {
    return a();
  } finally {
    A = c;
  }
};
var unstable_pauseExecution = function () {};
var unstable_requestPaint = function () {};
var unstable_runWithPriority = function (a, b) {
  switch (a) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      break;
    default:
      a = 3;
  }
  var c = A;
  A = a;
  try {
    return b();
  } finally {
    A = c;
  }
};
var unstable_scheduleCallback = function (a, b, c) {
  var d = u();
  "object" === typeof c && null !== c
    ? ((c = c.delay), (c = "number" === typeof c && 0 < c ? d + c : d))
    : (c = d);
  switch (a) {
    case 1:
      var e = -1;
      break;
    case 2:
      e = 250;
      break;
    case 5:
      e = 1073741823;
      break;
    case 4:
      e = 1e4;
      break;
    default:
      e = 5e3;
  }
  e = c + e;
  a = {
    id: y++,
    callback: b,
    priorityLevel: a,
    startTime: c,
    expirationTime: e,
    sortIndex: -1,
  };
  c > d
    ? ((a.sortIndex = c),
      f(x, a),
      null === h(w) && a === h(x) && (D ? (F(N), (N = -1)) : (D = !0), M(I, c - d)))
    : ((a.sortIndex = e), f(w, a), C || B || ((C = !0), L(J)));
  return a;
};
var unstable_shouldYield = O;
var unstable_wrapCallback = function (a) {
  var b = A;
  return function () {
    var c = A;
    A = b;
    try {
      return a.apply(this, arguments);
    } finally {
      A = c;
    }
  };
};
var unstable_Profiling = null;
export {
  unstable_IdlePriority,
  unstable_ImmediatePriority,
  unstable_LowPriority,
  unstable_NormalPriority,
  unstable_Profiling,
  unstable_UserBlockingPriority,
  unstable_cancelCallback,
  unstable_continueExecution,
  unstable_forceFrameRate,
  unstable_getCurrentPriorityLevel,
  unstable_getFirstCallbackNode,
  unstable_next,
  unstable_now,
  unstable_pauseExecution,
  unstable_requestPaint,
  unstable_runWithPriority,
  unstable_scheduleCallback,
  unstable_shouldYield,
  unstable_wrapCallback,
};
