{"extName": {"message": "Link Extractor Pro"}, "extDescription": {"message": "An efficient tool to extract all links from any webpage. It allows users to copy all links to the clipboard or export them in TXT, DOC, and PDF formats for easy management and sharing."}, "headerTitle": {"message": "Link Extractor Pro"}, "headerSubtitle": {"message": "Extract, copy, and export all links from the current page."}, "extractLinksButton": {"message": "Extract All Links"}, "extractingButton": {"message": "Extracting..."}, "scanningMessage": {"message": "Scanning page for links..."}, "noLinksFoundError": {"message": "No links found on this page."}, "extractionFailedError": {"message": "Failed to extract links. The page might be protected."}, "noActiveTabError": {"message": "Could not find an active tab. Please make sure you are on a webpage."}, "totalLinksFoundLabel": {"message": "Total Links"}, "totalSelectedLabel": {"message": "Selected"}, "selectAllLabel": {"message": "Select All"}, "copiedToClipboardMessage": {"message": "Copied to clipboard!"}, "copyFailedError": {"message": "Failed to copy to clipboard."}, "pdfFailedError": {"message": "Failed to generate PDF. The jsPDF library might not have loaded."}, "devModeMessage": {"message": "Running in dev mode. Using mock data."}, "copyAllButton": {"message": "Copy All"}, "openAllButton": {"message": "Open All"}, "exportTxtButton": {"message": "Export TXT"}, "exportDocButton": {"message": "Export DOC"}, "exportPdfButton": {"message": "Export PDF"}, "noTextForLink": {"message": "No text for this link"}, "initialStateMessage": {"message": "Click 'Extract All Links' to get started."}, "openingLinksMessage": {"message": "Opening all links in new tabs..."}, "openAllConfirmation": {"message": "This will open $1 tabs. Are you sure you want to continue?", "placeholders": {"1": {"content": "$1", "example": "25"}}}, "errorPrefix": {"message": "Error"}, "unsupportedPageError": {"message": "This extension cannot run on special browser pages (e.g., settings) or the Chrome Web Store."}, "protectedPageError": {"message": "This page's security policy prevents the extension from extracting links."}, "injectionFailedError": {"message": "Failed to run the script on this page. Please try reloading the tab."}}