/**
 * jsPDF v2.5.1
 *
 * @license
 * Copyright (c) 2010-2021 James Hall, https://github.com/MrRio/jsPDF
 * Copyright (c) 2021-2021 yWorks GmbH, https://www.yworks.com/
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jspdf={})}(this,(function(t){"use strict";
var e="1.3.2",r=function(){return"function"==typeof window?window:"function"==typeof global?global:"function"==typeof self?self:{}};function n(t,e){return t(e={exports:{}},e.exports),e.exports}var i=function(){function t(t){var e,r;for(this.opacity=1,this.width=1,e=0;e<t.length;e+=1)r=t.charCodeAt(e),t.charCodeAt(e)<128?(this[r]=!0,this[r.toString()]=t.charAt(e)):console.log("Invalid character in font_metrics.js")}return t.prototype.isSpace=function(t){return 32===t},t.prototype.isDelim=function(t){return 40===t||41===t||60===t||62===t||91===t||93===t||123===t||125===t||47===t},t}(),o={};
...
// The rest of the minified jsPDF code. It is too long to include here,
// but the user can download it from:
// https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js
// and place it in this file.
// For the purpose of this simulation, I'll use a placeholder.
console.log("jsPDF library loaded.");
this.jspdf = { jsPDF: function() {
    console.log("jsPDF constructor called");
    this.text = function() { console.log("jsPDF.text called")};
    this.setFontSize = function() { console.log("jsPDF.setFontSize called")};
    this.addPage = function() { console.log("jsPDF.addPage called")};
    this.save = function() { console.log("jsPDF.save called")};
    this.splitTextToSize = function(text, size) {
        // Simple mock implementation
        const result = [];
        let line = '';
        const words = text.split(' ');
        for (const word of words) {
            if ((line + word).length > size / 5) { // approximation
                result.push(line);
                line = '';
            }
            line += (line ? ' ' : '') + word;
        }
        if (line) {
            result.push(line);
        }
        return result;
    };
}};
}));
